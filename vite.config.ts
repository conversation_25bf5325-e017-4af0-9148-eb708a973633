import { sentryVitePlugin } from "@sentry/vite-plugin";
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import viteCompression from "vite-plugin-compression";

// https://vite.dev/config/
export default defineConfig({
    plugins: [react(), viteCompression({
        algorithm: "brotliCompress",
        ext: ".br",
        threshold: 1024,
    }), sentryVitePlugin({
        org: "good-ancestor",
        project: "javascript-react"
    })],
    clearScreen: false,
    build: {
        outDir: "dist",
        minify: true,
        cssMinify: true,
        cssCodeSplit: true,
        target: 'esnext',
        rollupOptions: {
            output: {
                sourcemap: true,
                sourcemapIgnoreList: (relativeSourcePath) => {
                    // will ignore-list all files with node_modules in their paths
                    return relativeSourcePath.includes('node_modules');
                }
            }
        }
    },
    define: {
        'process.env': process.env,
        'global': 'window'
    },
    resolve: {
        alias: {
            "@": "/src",
        },
    }
});
name: CICD Workflow (Manual Production Deploy)

on:
  workflow_dispatch:

env:
  ACTIONS_STEP_DEBUG: true
  

jobs:
  build:
    uses: ./.github/workflows/build.yaml 
    secrets: inherit
    with:
      CONFIG_FILENAME: "config.production.ts"
  deploy_production:
    environment: production
    runs-on: 
      group: KubeRunners
    name: Deploy Production
    needs: [build]
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Google Cloud SDK
        uses: google-github-actions/setup-gcloud@1bee7de035d65ec5da40a31f8589e240eba8fde5
        with:
          project_id: ${{ vars.PRODUCTION_GKE_PROJECT }}

      - name: 'gcloud install kubectl'
        run: |
          gcloud components install kubectl kustomize

      - id: 'auth'
        uses: 'google-github-actions/auth@v2'
        with:
          credentials_json: ${{ secrets.PRODUCTION_GKE_SA_KEY }}

      - name: Get GKE credentials
        uses: google-github-actions/get-gke-credentials@db150f2cc60d1716e61922b832eae71d2a45938f
        with:
          project_id: ${{ vars.PRODUCTION_GKE_PROJECT }}
          cluster_name: ${{ vars.GKE_PRODUCTION_CLUSTER_NAME }}
          location: ${{ vars.GKE_PRODUCTION_ZONE }}

      - uses: actions/download-artifact@v4
        with:
          name: version-tag

      - name: Confirm git commit SHA output
        run: |
          echo "IMAGE_TAG=${{ vars.IMAGE_NAME }}:$(cat ./version_tag.txt)" >> $GITHUB_ENV

      # Set up kustomize
      - name: Set up Kustomize
        run: |-
          curl -sfLo kustomize https://github.com/kubernetes-sigs/kustomize/releases/download/v3.1.0/kustomize_3.1.0_linux_amd64
          chmod u+x ./kustomize
      - name: Change image ref
        working-directory: ./k8s/overlays/${{ vars.K8S_OVERLAY }}
        run: kustomize edit set image ${{ env.IMAGE_TAG }}
      - name: see Kustomize Difference
        working-directory: ./k8s/overlays/${{ vars.K8S_OVERLAY }}
        run: kustomize build . | kubectl diff -f  -
        continue-on-error: true
      - name: Apply Kustomization
        working-directory: ./k8s/overlays/${{ vars.K8S_OVERLAY }}
        run: kubectl apply -k .
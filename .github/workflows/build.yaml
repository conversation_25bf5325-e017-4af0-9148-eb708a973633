name: <PERSON>uild & Push Docker Image

on: 
  workflow_call:
    inputs:
      CONFIG_FILENAME:
        required: true
        type: string

env:
  ACTIONS_STEP_DEBUG: true

jobs:
  build:
    # if: github.ref == 'refs/heads/main'
    name: Build & Push Docker
    runs-on: 
      group: KubeRunners
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set short git commit SHA
        id: vars
        run: |
          calculatedSha=v-$(echo ${{ github.ref_name }} | sed 's/[^a-zA-Z0-9._-]/-/g')-$(git rev-parse --short ${{ github.sha }})
          echo "VERSION_TAG=$calculatedSha" >> $GITHUB_ENV
      - name: Confirm git commit SHA output
        run: echo $VERSION_TAG
      - name: Confirm registry name
        run: |
          echo "${{ vars.IMAGE_NAME }}:$VERSION_TAG"
          echo "IMAGE_TAG=${{ vars.IMAGE_NAME }}:$VERSION_TAG" >> $GITHUB_ENV
          echo "$VERSION_TAG" > ./version_tag.txt
      - uses: actions/upload-artifact@v4
        with:
          name: version-tag
          path: ./version_tag.txt
      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Environment config
        run: |
          echo "Changing Environment config file to ${{ inputs.CONFIG_FILENAME }}"
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          build-args: |
            CONFIG_ENV=${{ inputs.CONFIG_FILENAME }}
            SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}
          context: .
          push: true
          tags: |
            ${{ env.IMAGE_TAG }}
            ${{ vars.IMAGE_NAME }}:latest
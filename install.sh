#!/bin/bash

# install nvm
if [ -d "${HOME}/.nvm/.git" ]; then 
    echo "nvm installed"; 
else 
    echo "nvm not installed, installing nvm"; 
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.2/install.sh | bash
fi
# source nvm to get nvm command
source ~/.nvm/nvm.sh
# install currect node version
nvm install
# install pnpm for managing packages
npm install -g pnpm
# install npm packages
pnpm install
# run dev
pnpm run dev
<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 501 301">
  <defs>
    <style>
      .cls-1 {
        fill: #fff;
        fill-rule: evenodd;
      }

      .cls-2 {
        fill: none;
        stroke: #fff;
        stroke-miterlimit: 10;
        strokeWidth: 2.31px;
      }
    </style>
  </defs>
  <g id="logo_00" data-name="logo 00">
    <g>
      <path class="cls-2" d="M120.07,189.07v-80.8l-5.45-6.44-11.4.5-16.36,7.93.99,8.43-8.43,1.49-8.43,24.29,4.46,4.46-4.96,4.96,5.45,11.4,19.83-1.49"/>
      <path class="cls-2" d="M120.07,189.07l-5.95,9.91-4.46-.99-5.45.99-8.43-3.97,24.29-36.68"/>
      <path class="cls-2" d="M96.77,163.79l-15.37,10.91,10.91,12.39,21.81,11.9"/>
      <path class="cls-2" d="M76.45,165.77l9.42,4.96"/>
      <path class="cls-2" d="M74.96,148.91l36.19,21.81,8.92,11.4"/>
      <path class="cls-2" d="M119.58,189.07l-19.83.99"/>
      <path class="cls-2" d="M87.85,118.18l8.43,15.37,1.98,15.37-.5,40.15"/>
      <path class="cls-2" d="M74.96,148.91l20.82-15.86,23.3-10.91"/>
      <path class="cls-2" d="M75.46,147.92l12.39-28.75,20.32-16.36"/>
      <path class="cls-2" d="M87.85,118.67l22.8,7.44,9.42,5.95"/>
      <path class="cls-2" d="M75.95,148.42h21.81l13.38-21.81,4.46-21.81"/>
      <path class="cls-2" d="M119.58,143.46l-21.32,5.45,13.38,21.32,1.49,18.84"/>
    </g>
    <path class="cls-2" d="M120.87,189.56h4.86"/>
    <path id="Shape_13_copy" data-name="Shape 13 copy" class="cls-2" d="M120.87,181.63h4.86"/>
    <path id="Shape_13_copy_2" data-name="Shape 13 copy 2" class="cls-2" d="M120.87,159.32h4.86"/>
    <path id="Shape_13_copy_3" data-name="Shape 13 copy 3" class="cls-2" d="M120.87,143.46h4.86"/>
    <path id="Shape_13_copy_4" data-name="Shape 13 copy 4" class="cls-2" d="M120.87,132.06h4.86"/>
    <path id="Shape_13_copy_5" data-name="Shape 13 copy 5" class="cls-2" d="M120.87,122.14h4.86"/>
    <g id="Group_1_copy_2" data-name="Group 1 copy 2">
      <path class="cls-2" d="M126.02,189.27v-80.95l5.45-6.44,11.4.5,16.36,7.93-.99,8.43,8.43,1.49,8.43,24.34-4.46,4.46,4.96,4.96-5.45,11.4-19.88-1.49"/>
      <path class="cls-2" d="M126.02,189.27l5.95,9.91,4.46-.99,5.45.99,8.43-3.97-24.29-36.73"/>
      <path class="cls-2" d="M149.37,163.93l15.37,10.91-10.91,12.39-21.86,11.9"/>
      <path class="cls-2" d="M169.7,165.92l-9.42,4.96"/>
      <path class="cls-2" d="M171.18,149.06l-36.24,21.86-8.92,11.4"/>
      <path class="cls-2" d="M126.52,189.27l19.88.99"/>
      <path class="cls-2" d="M158.29,118.28l-8.43,15.42-1.98,15.42.5,40.2"/>
      <path class="cls-2" d="M171.18,149.06l-20.87-15.91-23.35-10.91"/>
      <path class="cls-2" d="M170.69,148.07l-12.39-28.8-20.37-16.41"/>
      <path class="cls-2" d="M158.29,118.77l-22.85,7.44-9.42,5.95"/>
      <path class="cls-2" d="M170.19,148.57h-21.86l-13.38-21.86-4.46-21.86"/>
      <path class="cls-2" d="M126.52,143.61l21.37,5.45-13.38,21.37-1.49,18.89"/>
    </g>
  </g>
  <path id="holdings" class="cls-1" d="M218.03,185.94v-32.47h-8.13v12.69h-12.84v-12.69h-8.13v32.47h8.13v-13.04h12.84v13.04h8.13ZM256.5,169.68c0-8.33-5.65-17-17.4-17s-17.4,8.68-17.4,17,5.65,17,17.4,17,17.4-8.68,17.4-17ZM248.17,169.68c0,5.45-2.97,10.31-9.07,10.31s-9.07-4.86-9.07-10.31,2.97-10.36,9.07-10.36,9.07,4.91,9.07,10.36h0ZM283.91,185.94v-6.79h-15.57v-25.68h-8.13v32.47h23.7ZM317.03,169.54c0-5.01-1.78-9.32-5.16-12.29-4.11-3.57-9.62-3.77-15.07-3.77h-10.61v32.47h10.61c4.06,0,10.26,0,15.07-4.21,3.32-2.92,5.16-7.24,5.16-12.19ZM308.7,169.54c0,2.92-.79,5.65-2.63,7.44-2.23,2.23-5.06,2.33-8.03,2.33h-3.67v-19.33h3.67c3.02,0,6,0,8.03,2.08,1.93,1.98,2.63,4.76,2.63,7.49h0ZM328.87,185.94v-32.47h-8.13v32.47h8.13ZM363.13,185.94v-32.47h-7.63v19.88l-13.43-19.88h-8.08v32.47h7.63v-19.88l13.43,19.88h8.08ZM399.27,185.94v-18.69h-15.07v6.25h8.43c0,4.36-3.97,6.49-8.08,6.49-5.85,0-9.37-4.56-9.37-10.36,0-5.4,3.22-10.26,9.57-10.26,3.07,0,6.05,1.24,8.13,3.52l4.76-4.76c-.64-.74-4.66-5.45-13.19-5.45-11.45,0-17.6,7.88-17.6,17.05s5.85,16.95,16.26,16.95c6.2,0,9.77-3.47,10.01-3.77l.1,3.02h6.05ZM430.5,176.28c0-14.13-20.08-8.28-20.08-14.03,0-1.83,1.93-3.07,5.35-3.07,6.2,0,9.57,3.22,9.86,3.47l4.51-5.16c-.59-.59-4.56-4.81-14.33-4.81-8.08,0-13.48,4.06-13.48,9.96,0,13.43,19.98,8.13,19.98,14.08,0,2.28-2.53,3.32-6.35,3.32-3.72.05-7.34-1.19-10.21-3.57l-4.31,5.5c.59.59,5.11,4.71,14.52,4.71,8.23,0,14.52-3.67,14.52-10.41h0Z"/>
  <path id="Big" class="cls-1" d="M215.8,136.57c0-3.47-1.83-6.64-6.35-7.83.99-.45,5.06-2.38,5.06-8.08.05-2.13-.74-4.16-2.18-5.7-2.63-2.73-6.74-2.73-11.15-2.73h-12.19v34.21h11.85c5.11,0,9.96-.15,12.79-3.62,1.44-1.78,2.18-3.97,2.18-6.25ZM210.25,121.55c0,5.35-4.56,5.75-8.63,5.75h-8.48v-11.45h8.18c3.97,0,8.92,0,8.92,5.7ZM211.39,136.62c0,6.54-6.25,6.2-11.05,6.2h-7.19v-12h8.38c5.01,0,9.86.3,9.86,5.8h0ZM226.41,146.44v-34.21h-4.21v34.21h4.21ZM264.23,146.44v-17.99h-14.87v3.72h11.01v.99c0,3.42-2.08,10.26-10.96,10.26s-12.24-6.59-12.24-14.08,3.97-14.08,12.54-14.08c6.94,0,10.11,4.21,10.61,4.76l2.88-2.43c-.45-.69-4.21-6.15-13.48-6.15-10.86,0-17,7.88-17,17.85s5.6,17.95,16.06,17.95c8.53,0,11.3-4.71,11.75-5.4l.1,4.61h3.62Z"/>
  <path id="brain" class="cls-1" d="M316.83,136.57c0-3.47-1.83-6.64-6.35-7.83.99-.45,5.06-2.38,5.06-8.08.05-2.13-.74-4.16-2.18-5.7-2.63-2.73-6.74-2.73-11.15-2.73h-12.19v34.21h11.85c5.11,0,9.96-.15,12.79-3.62,1.44-1.78,2.18-3.97,2.18-6.25ZM311.28,121.55c0,5.35-4.56,5.75-8.63,5.75h-8.48v-11.45h8.18c4.02,0,8.92,0,8.92,5.7ZM312.47,136.62c0,6.54-6.25,6.2-11.05,6.2h-7.19v-12h8.38c4.96,0,9.86.3,9.86,5.8h0ZM351.13,146.44l-10.66-14.67c.59-.15,8.72-1.54,8.72-9.77.05-2.53-.94-5.01-2.78-6.79-2.73-2.73-6.64-2.92-11.01-2.92h-12.24v34.21h4.21v-14.33h8.43l10.26,14.28h5.06ZM344.99,122.19c0,6.1-5.65,6.44-10.76,6.44h-6.79v-12.84h7.14c3.22,0,6.74.05,8.87,2.18,1.04.99,1.54,2.28,1.54,4.21ZM385.83,146.44l-14.23-34.21h-5.06l-14.23,34.21h4.51l3.67-9.07h17l3.67,9.07h4.66ZM376.02,133.74h-14.03l6.99-17.25,7.04,17.25ZM394.61,146.44v-34.21h-4.21v34.21h4.21ZM430.5,146.44v-34.21h-3.92v27.36l-19.78-27.36h-4.36v34.21h3.92v-27.61l20.03,27.61h4.11Z"/>
</svg>
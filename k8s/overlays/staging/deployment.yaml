apiVersion: apps/v1
kind: Deployment
metadata:
  name: staging-thinkagents-ui
  labels:
    app: staging-thinkagents-ui
spec:
  replicas: 1
  selector:
    matchLabels:
      app: staging-thinkagents-ui
  template:
    metadata:
      labels:
        app: staging-thinkagents-ui
    spec:
      imagePullSecrets:
        - name: docker-cfg
      containers:
        - name: staging-thinkagents-ui
          image: plenipotentss/thinkagents-ui
          imagePullPolicy: Always
          ports:
            - containerPort: 80
              name: agent
          startupProbe:
            httpGet:
              path: /
              port: 80
            failureThreshold: 30
            periodSeconds: 10
          readinessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 10
          livenessProbe:
            httpGet:
              path: /
              port: 80
            initialDelaySeconds: 15
            periodSeconds: 10
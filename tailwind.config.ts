import type { Config } from "tailwindcss";
import tailwindAnimate from "tailwindcss-animate";
import tailwindTypography from "@tailwindcss/typography";
import tailwindForms from "@tailwindcss/forms";
import tailwindAspectRatio from "@tailwindcss/aspect-ratio";
import tailwindGroupPeerChecked from "@tushargugnani/tailwind-group-peer-checked";

import { withAccountKitUi, createColorSet } from "@account-kit/react/tailwind";

const config = {
	darkMode: ["class"],
	presets: [require('@ailayerlabs/six079-tailwind-preset')],
	plugins: [
		tailwindAnimate,
		tailwindTypography,
		tailwindForms,
		tailwindAspectRatio,
		tailwindGroupPeerChecked,
		function ({ addUtilities }) {
			const newUtilities = {
				'.border-reply': {
					'border-left-style': 'dotted',
					'border-left-width': '3px',
					'border-left-color': '#4B4B4B',
				},
			};

			addUtilities(newUtilities, ['responsive', 'hover']);
		},
	],
	content: ["src/**/*.{ts,tsx}", "components/**/*.{ts,tsx}"],
	safelist: [
		'text-orange',
		'text-purple',
		'text-green',
		'text-red',
		'text-teal',
		'text-pink',
		'text-sand',
		'text-gold',
		'bg-orange',
		'bg-purple',
		'bg-green',
		'bg-red',
		'bg-teal',
		'bg-pink',
		'bg-sand',
		'bg-gold',
		'bg-gradient-orange',
		'bg-gradient-purple',
		'bg-gradient-green',
		'bg-gradient-red',
		'bg-gradient-teal',
		'bg-gradient-pink',
		'bg-gradient-sand',
		'bg-gradient-gold',
		'border-[#CF0029]',
	],
	theme: {
		extend: {
			colors: {
				red: '#AA5E5E',
			},
			spacing: {
				1.6: '0.4rem',
			},
			keyframes: {
				logoGlow: {
					'0%, 100%': {
						boxShadow: '0 0 10px rgba(207,0,41,0.4)',
						backgroundColor: 'rgba(207,0,41,0.1)',
					},
					'50%': {
						boxShadow: '0 0 20px rgba(207,0,41,0.4)',
						backgroundColor: 'rgba(207,0,41,0.2)',
					},
				},
			},
			animation: {
				logoGlow: 'logoGlow 2s infinite',
			},
			fontFamily: {
				'fira-sans-extra-condensed': ['"Fira Sans Extra Condensed"', 'sans-serif'],
				'evolver': ['evolver-variable', 'sans-serif'],
				'mono': [
					'IBM Plex Mono',
					'ibm-plex-mono',
					'monospace',
				],
			},
			fontWeight: {
				'fira-thin': '100',
				'fira-medium': '500',
			},
		},
	},
} satisfies Config;

// wrap your existing tailwind config with 'withAccountKitUi'
export default withAccountKitUi(
	config,
	{
		// override account kit themes
		colors: {
			"btn-primary": createColorSet("#0cc1da", "#0cc1da"),
			"fg-accent-brand": createColorSet("#0cc1da", "#0cc1da"),
		},
		borderRadius: "lg",
	},
);


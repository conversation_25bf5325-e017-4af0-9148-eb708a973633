# Use a specific Node.js version for better reproducibility
FROM node:23.3.0-slim AS builder

RUN npm install -g pnpm@9.4.0 && \
    apt-get update && \
    apt-get install -y git python3 make g++ && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY ./  ./

RUN rm -rf node_modules/

ARG CONFIG_ENV=config.production.ts
ENV CONFIG_ENV=$CONFIG_ENV
RUN rm src/environments/config.ts
RUN echo "Changing config to: src/environments/$CONFIG_ENV"
RUN mv "src/environments/$CONFIG_ENV" src/environments/config.ts

ENV NODE_OPTIONS=--max-old-space-size=32768
ARG SENTRY_AUTH_TOKEN
ENV SENTRY_AUTH_TOKEN=$SENTRY_AUTH_TOKEN
RUN pnpm install \
    && pnpm build

FROM nginx:alpine
ENV NODE_ENV=production

COPY --from=builder /app/dist /usr/share/nginx/html
RUN chmod -R 755 /usr/share/nginx/html
COPY <<EOF /etc/nginx/conf.d/default.conf
server {
    listen 80;
    server_name localhost;
    root /usr/share/nginx/html;
    include /etc/nginx/mime.types;
  
    error_log /var/log/nginx/error.log debug;

    location / {
        index index.html;
        try_files \$uri \$uri/ /index.html =404;
    }

    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|webmanifest|mp4|br)\$ {
        expires max;
        try_files \$uri \$uri/ =404;
    }

    gzip            on;
    gzip_vary       on;
    gzip_http_version  1.0;
    gzip_comp_level 5;
    gzip_types
                    application/atom+xml
                    application/javascript
                    application/json
                    application/rss+xml
                    application/vnd.ms-fontobject
                    application/x-font-ttf
                    application/x-web-app-manifest+json
                    application/xhtml+xml
                    application/xml
                    font/opentype
                    image/svg+xml
                    image/x-icon
                    text/css
                    text/plain
                    text/x-component;
    gzip_proxied    no-cache no-store private expired auth;
    gzip_min_length 256;
    gunzip          on;
} 
EOF


EXPOSE 8080
CMD ["nginx","-g","daemon off;"]
{"name": "client", "private": true, "sideEffects": false, "type": "module", "scripts": {"extract-version": "./version.sh", "dev": "pnpm run extract-version && vite", "build": "pnpm run extract-version && tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "docker:deploy": "bash ./scripts/docker.sh build && bash ./scripts/docker.sh push", "docker:build": "bash ./scripts/docker.sh build", "docker:push": "bash ./scripts/docker.sh push", "docker:run": "bash ./scripts/docker.sh run", "docker:bash": "bash ./scripts/docker.sh bash", "docker:start": "bash ./scripts/docker.sh start", "i18n:extract": "node scripts/extract-i18n.js", "i18n:scan": "node scripts/extract-i18n.js src"}, "dependencies": {"@account-kit/core": "^4.13.0", "@account-kit/infra": "^4.13.0", "@account-kit/react": "^4.13.0", "@ailayerlabs/six079-tailwind-preset": "^0.0.3", "@alch/alchemy-web3": "^1.4.7", "@elizaos/core": "0.1.8-alpha.1", "@futureverse/auth-react": "^4.1.5", "@futureverse/auth-ui": "^0.12.6", "@kanety/stimulus-accordion": "^1.2.0", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@react-spring/web": "^9.7.5", "@react-three/drei": "^10.2.0", "@react-three/fiber": "9.1.1", "@sentry/react": "^9.15.0", "@sentry/vite-plugin": "^3.3.1", "@tanstack/react-query": "^5.66.0", "@tsparticles/preset-confetti": "^3.2.0", "@tsparticles/react": "^3.0.0", "@tsparticles/shape-image": "^3.8.1", "@types/big.js": "^6.2.2", "@uidotdev/usehooks": "^2.4.1", "alchemy-sdk": "^3.5.6", "big.js": "^6.2.2", "bigjs": "^0.0.3", "class-variance-authority": "^0.7.1", "clsx": "2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "ethers": "^6.13.5", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "js-cookie": "^3.0.5", "lucide-react": "^0.469.0", "plyr": "^3.7.8", "react": "^19.0.0", "react-aiwriter": "^1.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.6.0", "react-router": "^7.1.5", "react-router-dom": "^7.1.5", "react-youtube": "^10.1.0", "semver": "^7.6.3", "three": "0.172.0", "viem": "2.30.0", "vite-plugin-compression": "^0.5.1", "wagmi": "^2.15.4", "web3-utils": "^4.3.3", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tushargugnani/tailwind-group-peer-checked": "^1.0.0", "@types/js-cookie": "^3.0.6", "@types/node": "^22.10.5", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/semver": "^7.5.8", "@types/three": "^0.177.0", "@types/youtube-player": "^5.5.11", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "@vitejs/plugin-react-swc": "^3.7.2", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "postcss-flexbugs-fixes": "^5.0.2", "postcss-import": "^16.1.0", "postcss-nested": "^6.2.0", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "~5.6.3", "typescript-eslint": "^8.19.1", "vite": "^6.0.7", "vite-tsconfig-paths": "^5.1.4"}}
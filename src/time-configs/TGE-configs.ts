
const TGE_START = new Date("Thu Jun 10 2025 23:36:27 GMT-0700 (Pacific Daylight Time)");

// TGE Bonus ends 14 days after TGE start
const TGE_BONUS_ENDS = new Date(TGE_START);
TGE_BONUS_ENDS.setDate(TGE_BONUS_ENDS.getDate() + 14);

// TGE Bonus ends 14 days after TGE start
const ASTO_SWAP_ENDS = new Date(TGE_START);
ASTO_SWAP_ENDS.setDate(ASTO_SWAP_ENDS.getDate() + 14);

// TGE Claim ends 30 days after TGE start
const TGE_END = new Date(TGE_START);
TGE_END.setDate(TGE_END.getDate() + 30);

// Group 1 Stake Ends after 30 days from TGE
const TGE_STAKE_ONE_ENDS = new Date(TGE_START);
TGE_STAKE_ONE_ENDS.setDate(TGE_STAKE_ONE_ENDS.getDate() + 30);

// Group 2 Stake Ends after 60 days from TGE
const TGE_STAKE_TWO_ENDS = new Date(TGE_START);
TGE_STAKE_TWO_ENDS.setDate(TGE_STAKE_TWO_ENDS.getDate() + 60);

// Group 3 Stake Ends after 30 days from tGE
const TGE_STAKE_THREE_ENDS = new Date(TGE_START);
TGE_STAKE_THREE_ENDS.setDate(TGE_STAKE_THREE_ENDS.getDate() + 90);


const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secondsClean = seconds % 60;

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secondsClean.toFixed(0).padStart(2, '0')}`;
}

export {
    TGE_START,
    TGE_BONUS_ENDS,
    ASTO_SWAP_ENDS,
    TGE_END,
    TGE_STAKE_ONE_ENDS,
    TGE_STAKE_TWO_ENDS,
    TGE_STAKE_THREE_ENDS,
    formatTime
}

import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { TooltipProvider } from "./components/ui/tooltip";
import { Toaster } from "./components/ui/toaster";
import { BrowserRouter, Navigate, Route, Routes } from "react-router";
import Home from "./routes/home";
import useVersion from "./hooks/use-version";
import { AccountKitProviders } from './account-kit/AccountKitProvider'
import { config, queryClient } from './account-kit/config'
import { Header } from "./components/layouts/header";
import { Link } from "react-router";
import { cookieToInitialState } from '@account-kit/core'
import { SVGDefs } from "./components/ui/svg-defs";
import "./lib/i18n"; // Initialize i18n
import { Footer } from "./components/layouts/footer";
import About from "./routes/about";
import Investors from "./routes/investors";
import Partners from "./routes/partners";
import FAQ from "./routes/faq";
import ScrollToTop from "./components/scroll-to-top";
import Contact from "./routes/contact";
import PrivacyPolicy from "./routes/privacy-policy";
import Terms from "./routes/terms";
import NotFound404 from "./routes/not-found-404";
import { AuthProvider } from "./components/auth/AuthContext";
import Dashboard from "./routes/dashboard/dashboard";
import Token from "./routes/dashboard/token";
import DashboardSettings from "./routes/dashboard/settings";
import Blog from "./routes/blog";
import Post from "./routes/post";
import ToggleLogin from "./routes/toggle-login";
import * as Sentry from "@sentry/react";
import FVLogin from "./routes/fvlogin";
import FVLogout from "./routes/fvlogout";
import Thinkubator from "./routes/thinkubator";
import Claim from "./routes/claim";
import env from "@/environments/config";
import { StakeProvider } from "./components/think-provider/StakeProvider";

if (import.meta.env.PROD) {
    Sentry.init({
        dsn: "https://<EMAIL>/****************",
        // Setting this option to true will send default PII data to Sentry.
        // For example, automatic IP address collection on events
        sendDefaultPii: true
    });
}

function App() {
    useVersion();
    const initialState = cookieToInitialState(
        config,
        document.cookie ?? undefined
    );
    return (
        <QueryClientProvider client={queryClient}>
            <AccountKitProviders initialState={initialState}>
                <div>
                    <SVGDefs />
                    <BrowserRouter>
                        <ScrollToTop />
                        <AuthProvider>
                            <Header />
                            <Routes>
                                { env.WALLET_LOGIN ? (
                                    <Route path="/" element={<Claim />} />
                                ) : (
                                    <Route path="/" element={<Home />} />
                                )}
                                <Route path="/" element={<Home />} />
                                <Route path="/fvlogin" element={<FVLogin />} />
                                <Route path="/fvlogout" element={<FVLogout />} />
                                <Route path="/contact" element={<Contact />} />
                                <Route path="/about" element={<About />} />
                                <Route path="/investors" element={<Investors />} />
                                <Route path="/partners" element={<Partners />} />
                                <Route path="/faq" element={<FAQ />} />
                                <Route path="/contact" element={<Contact />} />
                                <Route path="/blog" element={<Blog />} />
                                <Route path="/blog/:slug" element={<Post />} />
                                <Route path="/privacy-policy" element={<PrivacyPolicy />} />
                                <Route path="/terms-and-conditions" element={<Terms />} />
                                <Route path="/dashboard" element={<Dashboard />} />
                                <Route path="/dashboard/token" element={<Token />} />
                                <Route path="/dashboard/settings" element={<DashboardSettings />} />
                                <Route path="/products/thinkubator" element={<Thinkubator />} />

                                <Route path="/claim" element={env.WALLET_LOGIN ? <Navigate to="/" /> : <NotFound404 />} />
                                <Route path="/404" element={<NotFound404 />} />

                                <Route path="/toggle-login" element={<ToggleLogin />} />
                                <Route path="*" element={<NotFound404 />} />
                            </Routes>
                            <Toaster />
                            <Footer />
                        </AuthProvider>
                    </BrowserRouter>
                </div>
            </AccountKitProviders>
        </QueryClientProvider>
    );
}

export default App;

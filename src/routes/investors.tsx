import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { apiClient, isGetInvestorsResponse } from "@/lib/api";
import { LogoImage } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { NavLink } from "react-router";
import env from "@/environments/config";
import { useTranslation } from "@/hooks/useTranslation";

export default function Investors() {
    const { t, currentLanguage } = useTranslation();
    const query = useQuery({
        queryKey: ["investors"],
        queryFn: async () => {
            const data = await apiClient.getInvestors(currentLanguage);
            if (isGetInvestorsResponse(data)) {
                return data;
            }
            return null;
        },
        retry: 1,
        refetchOnWindowFocus: "always",
    });
    return (
        <div>
            <div className="mb-8 text-off-white w-full mx-auto px-4 pt-8 md:pt-12 lg:pt-20 md:px-16 lg:px-16">
                <header className="flex flex-row justify-center items-center gap-4 ">
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                    <h2 className="text-6xl md:text-6xl lg:text-9xl font-token">{t('pages.investors.title')}</h2>
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                </header>
            </div>
            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">

                <h2 className="pt-4 uppercase text-4xl font-bold text-off-white font-token text-center mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.investors.hero.title')}
                </h2>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">    
                     {t('pages.investors.hero.description')}
                </p>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">    
                </p>

                <section className="py-10 px-4 lg:px-20 max-w-screen-2xl mx-auto" data-controller="logos">
                    <div className="flex flex-wrap justify-center items-center gap-8 lg:gap-24">
                        {query && query.data && query.data.investors && query.data.investors.map((logo: LogoImage) => {
                            return (
                                <NavLink to={logo.url ? logo.url : "#"} key={logo.name} target="_blank" title={logo.name}>
                                    { logo.image && <img src={`${env.API_BASE_URL}${logo.image}`} className="inline-block w-full max-w-20 md:max-w-20 lg:max-w-40 max-h-100 md:max-h-100 lg:max-h-20 object-contain" /> }
                                </NavLink>
                            );
                        })}
                    </div>
                </section>

            </PrimaryContainerComponent>
            <div className="mt-16 w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}
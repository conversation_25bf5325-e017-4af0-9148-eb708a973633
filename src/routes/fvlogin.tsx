import React, { useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';

// TODO: These should be configured via environment variables
const CLIENT_ID = 'YOUR_CLIENT_ID'; // Replace with your actual client ID
const REDIRECT_URI = window.location.origin + '/fvlogin'; // Or your specific registered redirect URI

const FVLogin: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const iss = searchParams.get('iss'); // Issuer URL

    const loginAsync = async () => {
      if (!code || !state || !iss) {
        console.error('Missing required query parameters for login.');
        // TODO: Handle missing parameters (e.g., redirect to an error page or back to initial login step)
        navigate('/'); // Or an error page
        return;
      }

      // 1. Verify State
      const savedState = localStorage.getItem('oauth_state');
      if (state !== savedState) {
        console.error('Invalid state parameter.');
        // TODO: Handle invalid state (CSRF attack detected)
        navigate('/'); // Or an error page
        return;
      }
      localStorage.removeItem('oauth_state'); // Clean up stored state

      // 2. Exchange Authorization Code for Tokens
      const codeVerifier = localStorage.getItem('oauth_code_verifier');
      if (!codeVerifier) {
        console.error('Code verifier not found.');
        // TODO: Handle missing code verifier
        navigate('/'); // Or an error page
        return;
      }
      localStorage.removeItem('oauth_code_verifier'); // Clean up stored code verifier

      const tokenEndpoint = `${iss}/token`; // Construct token endpoint from issuer

      const body = new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: REDIRECT_URI,
        client_id: CLIENT_ID,
        code_verifier: codeVerifier,
      });

      try {
        const response = await fetch(tokenEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: body.toString(),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: 'Failed to exchange token and parse error response.' }));
          console.error('Token exchange failed:', response.status, errorData);
          // TODO: Handle token exchange failure (e.g., show error message to user)
          navigate('/'); // Or an error/login page
          return;
        }

        const tokenResponse = await response.json();

        // 3. Store Tokens
        localStorage.setItem('access_token', tokenResponse.access_token);
        localStorage.setItem('refresh_token', tokenResponse.refresh_token);
        localStorage.setItem('id_token', tokenResponse.id_token);

        console.log('Login successful, tokens stored.');

        // 4. Redirect to Homepage
        navigate('/');

      } catch (error) {
        console.error('Error during login process:', error);
        // TODO: Handle other errors (e.g., network error)
        navigate('/'); // Or an error page
      }
    };

    loginAsync();
  }, [searchParams, navigate]);

  return (
    <div>
      <h1>Futureverse Login</h1>
      <p>Processing login...</p>
      {/* You can add a loading spinner or message here */}
    </div>
  );
};

export default FVLogin;

import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { IconChevronDown } from "@/components/ui/IconChevronDown";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { useTranslation } from "@/hooks/useTranslation";

export default function NotFound404() {
    const { t } = useTranslation();

    return (
        <div>

            <header className="relative flex flex-col justify-between items-center w-full h-full">
                <div className="relative w-full flex flex-col justify-center items-center p-6 overflow-x-clip">
                <video autoPlay={true} loop muted playsInline={true} className="absolute w-full left-[55.6875%] top-[74%] lg:left-[49.5%] lg:top-[74%] -translate-x-1/4 -translate-y-1/2 scale-[2.5] lg:scale-[2] z-[-1] opacity-45">
                    <source src="videos/hands-sphere-hologram.mp4" type="video/mp4" />
                </video>
                <div className="flex justify-center">
                    <img src="/images/logos/knocked_out_coin_w_rays.svg" className="block w-[37.5vw] lg:w-[30vw]" />
                </div>
                </div>
                <h1 className="heading-display text-off-white text-center mb-3 max-w-sm md:max-w-lg lg:max-w-3xl xl:max-w-5xl">{t('pages.notFound.title')}</h1>
            </header>
            <div className="w-full mt-32 linear-gradient-teal-to-sand h-[5px]"></div>
            <div className="w-full px-4 py-12 md:py-20 md:max-w-7xl mx-auto md:px-16 lg:px-24">
            <h1 className="heading-display text-off-white text-center mb-3 max-w-sm md:max-w-lg lg:max-w-3xl xl:max-w-5xl">{t('pages.notFound.subtitle')}</h1>
            </div>

        </div>
    );
}
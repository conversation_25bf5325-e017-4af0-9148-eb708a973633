import useTranslation from "@/hooks/useTranslation";
import React from "react";

export default function NotFound404() {
  const { t } = useTranslation();
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-black px-4 py-12">
      <img
        src="/images/404-500-Disrupted.svg"
        alt="Agent Pathway Disrupted"
        className="w-48 md:w-72 lg:w-80 mb-8"
        draggable={false}
      />
      <div className="font-mono text-teal max-w-prose text-base md:text-lg text-left">
        <div className="text-lg md:text-xl font-bold mb-6 text-center">
          {t('pages.notFound.title')}
        </div>
        <p className="mb-4">Dear Explorer,</p>
        <p className="mb-4">You’ve reached the edge.</p>
        <p className="mb-4">This isn’t the page you were looking for. But that’s okay. It’s not just a broken link. It’s a signal from the future.</p>
        <p className="mb-4">The old internet was made of pages. The new one will be made of agents. Intelligent, evolving, on-chain agents. Owned by people, not platforms. And you’re standing at the frontier of that shift.</p>
        <p className="mb-4">We call them Souls.</p>
        <p className="mb-4">They’re not chatbots. Not assistants. Not surveillance machines dressed up as help. They are composable, interoperable, self-improving intelligences. They are designed to think with you, grow beside you, and act on your behalf. Not Big Tech’s.</p>
        <p className="mb-4">Every Soul begins with a tokenized identity. It builds memory over time. It learns through experience. It lives across chains. And most importantly, it is yours. Not rented. Not harvested. Not extracted.</p>
        <p className="mb-4">We are building a new web. An agentic web. A place where intelligence is modular, decentralized, and free to evolve without permission.</p>
        <p className="mb-4">If this detour taught you anything, let it be this:</p>
        <p className="mb-4">You’re not lost. You’re early.</p>
        <p className="mb-4">And in this network, every misclick is a chance to... </p>
        <p className="mt-8 font-bold">Think for Yourself.</p>
      </div>
    </div>
  );
}
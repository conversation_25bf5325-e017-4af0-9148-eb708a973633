import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { apiClient } from "@/lib/api";
import { FAQItem, TeamMember } from "@/types";
import { useQuery } from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import env from "@/environments/config";
import { useTranslation } from "@/hooks/useTranslation";
import { ThinkProvider } from "@/components/think-provider/ThinkProvider";
import ThinkTokenCard from "@/components/ThinkTokenCard/ThinkTokenCard";
import { IconChevronDown } from "@/components/ui/IconChevronDown";

export default function Claim() {
    const { t } = useTranslation();
    const [openedIds, setOpenedIds] = useState<number[]>([]);

    function toggleOpened(event: any, id: number) {
        event.preventDefault();
        if (openedIds.includes(id)) {
            setOpenedIds(openedIds.filter((i) => i !== id));
        } else {
            setOpenedIds([...openedIds, id]);
        }
        console.log(openedIds)
    }

    const faqs = [
        {
            id: 0,
            question: t('pages.claim.faqs.whatIsThink.question'),
            answer: t('pages.claim.faqs.whatIsThink.answer'),
            is_open: true,
            created_at: new Date(),
            updated_at: new Date(),
        },
        {
            id: 1,
            question: t('pages.claim.faqs.howDifferentFromAsto.question'),
            answer: t('pages.claim.faqs.howDifferentFromAsto.answer'),
            is_open: false,
            created_at: new Date(),
            updated_at: new Date(),
        },
        {
            id: 2,
            question: t('pages.claim.faqs.whoBehindThink.question'),
            answer: t('pages.claim.faqs.whoBehindThink.answer'),
            is_open: false,
            created_at: new Date(),
            updated_at: new Date(),
        },
        {
            id: 3,
            question: t('pages.claim.faqs.howManyTokens.question'),
            answer: t('pages.claim.faqs.howManyTokens.answer'),
            is_open: false,
            created_at: new Date(),
            updated_at: new Date(),
        },
        {
            id: 4,
            question: t('pages.claim.faqs.howCanIBuild.question'),
            answer: t('pages.claim.faqs.howCanIBuild.answer'),
            is_open: false,
            created_at: new Date(),
            updated_at: new Date(),
        },
        {
            id: 5,
            question: t('pages.claim.faqs.canIStake.question'),
            answer: t('pages.claim.faqs.canIStake.answer'),
            is_open: false,
            created_at: new Date(),
            updated_at: new Date(),
        },
        {
            id: 6,
            question: t('pages.claim.faqs.whatIsStaking.question'),
            answer: t('pages.claim.faqs.whatIsStaking.answer'),
            is_open: false,
            created_at: new Date(),
            updated_at: new Date(),
        },
        {
            id: 7,
            question: t('pages.claim.faqs.whyCantWithdraw.question'),
            answer: t('pages.claim.faqs.whyCantWithdraw.answer'),
            is_open: false,
            created_at: new Date(),
            updated_at: new Date(),
        },
    ];

    useEffect(() => {
        faqs.forEach((faq: FAQItem) => {
            if (faq.is_open) {
                setOpenedIds([...openedIds, faq.id]);
            }
        });
    }, []);

    return (
        <div>
            <header className="relative flex flex-col justify-between items-center w-full h-full">
                <div className="relative w-full flex flex-col justify-center items-center p-6 overflow-x-clip">
                    <video style={{
                        maskImage: "linear-gradient(to bottom, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 0) 100%)"
                    }} autoPlay={true} loop muted playsInline={true} className="absolute w-full left-[50%] top-[25%] -translate-x-1/2 -translate-y-1/2 scale-[2] z-[-1] max-h-[1000px]">
                        <source src="videos/coin-fall-opt.mp4" type="video/mp4" />
                    </video>
                </div>

                <a id="claim"></a>
                <h1 className="heading-display text-off-white text-center mb-3 max-w-sm md:max-w-lg lg:max-w-3xl xl:max-w-5xl">{t('pages.claim.title')}</h1>

                <h2 className="pt-4 uppercase text-4xl font-bold font-token text-center mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl text-sand">
                    {t('pages.claim.subtitle')}
                </h2>

            </header>

            <div className="w-full max-w-5xl mx-auto px-4">
                <ThinkProvider>
                    <ThinkTokenCard />
                </ThinkProvider>
            </div>

            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">

                <div className="relative w-full min-h-[500px] flex flex-col justify-center items-center overflow-visible">

                    <div className="text-off-white w-full mx-auto px-4 pt-12 md:pt-16 lg:px-16">
                        <header className="flex flex-row justify-center items-center gap-4 ">
                            <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                            <h2 className="font-token uppercase text-2xl lg:text-3xl xl:text-6xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent lg:col-span-3 place-self-center">{t('pages.claim.faqTitle')}</h2>
                            <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                        </header>
                    </div>

                    <div className="mt-12 md:px-4 lg:px-16 w-full justify-center items-stretch">

                        <section id="faq" className="my-12">
                            <div className="st-accordion" data-controller="accordion">
                                {faqs.map((faq: FAQItem) => {
                                    const isOpen = openedIds.includes(faq.id);
                                    return (
                                        <div key={faq.id}>
                                            <div>
                                                <a href="#" className={`st-accordion__icon flex flex-row justify-between py-6  ${isOpen ? 'st-accordion__icon--opened' : ''}`} onClick={(e) => toggleOpened(e, faq.id)}>
                                                    <div className="font-token text-off-white text-2xl flex-grow">{faq.question}</div>
                                                    <span className="flex flex-direction row justify-end items-start w-10 flex-shrink">
                                                        <IconChevronDown className="w-6 text-off-white" />
                                                    </span>
                                                </a>
                                            </div>
                                            <div className={`st-accordion__content border-b border-black-700 font-mono text-off-white ${isOpen ? 'st-accordion__content--visible' : ''}`}>
                                                <div className="py-6 px-2" dangerouslySetInnerHTML={{ __html: faq.answer || "" }}>
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </section>
                    </div>
                </div>


                <div className="relative w-full pb-24 min-h-[500px] flex flex-col justify-center items-center overflow-visible">

                    <div className="text-off-white w-full mx-auto px-4 pt-12 md:pt-16 lg:px-16">
                        <header className="flex flex-row justify-center items-center gap-4 ">
                            <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                            <h2 className="font-token uppercase text-2xl lg:text-3xl xl:text-6xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent lg:col-span-3 place-self-center">{t('pages.claim.benefitsTitle')}</h2>
                            <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                        </header>
                    </div>

                    <div className="mt-12 md:px-4 lg:px-10 w-full justify-center items-stretch grid grid-cols-1 lg:grid-cols-2 gap-4">

                        <div className="lg:px-6 mt-2">
                            <h3 className="lg:pt-4 text-2xl font-bold text-off-white font-token mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">{t('pages.claim.benefits.professionalAgents.title')}</h3>
                            <p className="font-mono text-black-400">{t('pages.claim.benefits.professionalAgents.description')}</p>
                        </div>

                        <div className="lg:px-6 mt-2">
                            <h3 className="lg:pt-4 text-2xl font-bold text-off-white font-token mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">{t('pages.claim.benefits.ownExperience.title')}</h3>
                            <p className="font-mono text-black-400">{t('pages.claim.benefits.ownExperience.description')}</p>
                        </div>

                        <div className="lg:px-6 mt-2">
                            <h3 className="lg:pt-4 text-2xl font-bold text-off-white font-token mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">{t('pages.claim.benefits.patentProtection.title')}</h3>
                            <p className="font-mono text-black-400">Innovate confidently under $THINK’s patent umbrella, shielded from malicious infringement claims so you can focus on building.</p>
                        </div>

                        <div className="lg:px-6 mt-2">
                            <h3 className="lg:pt-4 text-2xl font-bold text-off-white font-token mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">{t('pages.claim.benefits.seamlessInteroperability.title')}</h3>
                            <p className="font-mono text-black-400">{t('pages.claim.benefits.seamlessInteroperability.description')}</p>
                        </div>

                    </div>
                </div>
            </PrimaryContainerComponent>


            <div className="w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}
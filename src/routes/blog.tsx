import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { apiClient } from "@/lib/api";
import { Post } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { NavLink } from "react-router";
import env from "@/environments/config";
import { format } from 'date-fns';
import { Loading } from "@/components/ui/loading";
import { useTranslation } from "@/hooks/useTranslation";

export default function Blog() {
    const { t, currentLanguage } = useTranslation();
    const posts = useQuery({
        queryKey: ["blog-posts"],
        queryFn: async () => {
            const data = await apiClient.getBlogPosts(9, 0, currentLanguage);
            return data;
        },
        retry: 1,
        refetchOnWindowFocus: "always",
    });

    if (posts && posts.isLoading) {
        return <Loading />;
    }
    return (
        <div>
            <div className="mb-8 text-off-white w-full mx-auto px-4 pt-8 md:pt-12 lg:pt-20 md:px-16 lg:px-16">
                <header className="flex flex-row justify-center items-center gap-4 ">
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                    <h2 className="text-6xl md:text-6xl lg:text-9xl font-token">{t('pages.blog.title')}</h2>
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                </header>
            </div>
            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">

                <div className="w-full mx-auto px-4 md:px-8 lg:px-10">
                    <div className="mt-10 md:columns-1 lg:grid lg:grid-cols-2 xl:grid-cols-3 w-full">
                        {posts && posts.data && posts.data['posts'] && posts.data['posts'].map((post: Post) => {
                            const formattedDate = format(post.created_at, 'MMMM d, yyyy');
                            let image = null;
                            if (post.preview_image) {
                                image = post.preview_image;
                            } else if (post.image) {
                                image = post.image;
                            }
                            return (
                                <div key={post.slug} className="relative group px-6 lg:px-6 overflow-hidden mb-3lg:overflow-visible cursor-default">
                                    <NavLink to={`/blog/${post.slug}`} title={post.title}>
                                        <div className="relative bg-white w-full pb-[75%] z-0">
                                        { image ? 
                                            <img src={`${env.API_BASE_URL}${image}`} className="absolute block w-full h-full inset-0 object-fill z-10" /> :
                                            null
                                        }
                                        </div>

                                        <div className="min-h-full top-0 pt-10 pb-14 overflow-hidden">
                                        <h4 className="font-mono text-gray-400 text-xs lg:text-sm font-bold mb-4">{formattedDate}</h4>
                                        <div className="font-mono text-white">
                                            <div className="text-sm lg:text-base mb-8">
                                                {post.title}
                                            </div>
                                        </div>
                                        </div>
                                    </NavLink>
                                </div>

                                
                                    
                            );
                        })}
                    </div>
                </div>
            </PrimaryContainerComponent>
            <div className="mt-16 w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}
import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import React from "react";
import YouTube from "react-youtube";
import { Options, YouTubePlayer } from 'youtube-player/dist/types';
import ThinkTokenCard from '@/components/ThinkTokenCard/ThinkTokenCard';
import { useAuth } from '@/components/auth/AuthContext';
import env from "@/environments/config";
import { ThinkProvider } from "@/components/think-provider/ThinkProvider";
import { NavLink } from "react-router";
import { useTranslation } from "@/hooks/useTranslation";

export default function Home() {
    const [showVideo, setShowVideo] = React.useState(false);
    const [videoPlayer, setVideoPlayer] = React.useState<YouTubePlayer | null>(null);
    // const isLoggedIn = true;
    const { user } = useAuth();
    const { t } = useTranslation();
    const tokenBurnEnabled = env.TOKEN_BURN_ENABLED;

    // console.log("user", user);

    function toggleVideo() {
        const newShowVideo = !showVideo
        setShowVideo(newShowVideo);
        console.log(newShowVideo)
        console.log(videoPlayer)
        if (videoPlayer !== null) {
            if (newShowVideo) {
                videoPlayer.playVideo();
            } else {
                videoPlayer.pauseVideo();
            }
        }
    }
    function onReady(event: any) {
        setVideoPlayer(event.target);
        event.target.pauseVideo();
    }

    function onPuase() {
        toggleVideo();
    }

    const options = {
        playerVars: {
            autoplay: 0,
            controls: 1,
            enablejsapi: 1,
            rel: 0
        },
    } as Options;

    return (
        <div>
            <header className="relative flex flex-col justify-between items-center w-full h-full">
                <div className="relative w-full flex flex-col justify-center items-center p-6 overflow-x-clip">
                    <video autoPlay={true} loop muted playsInline={true} className="absolute w-full left-[55.6875%] top-[74%] lg:left-[49.5%] lg:top-[74%] -translate-x-1/4 -translate-y-1/2 scale-[2.5] lg:scale-[2] z-[-1] opacity-45">
                        <source src="videos/hands-sphere-hologram.mp4" type="video/mp4" />
                    </video>
                    <div className="flex justify-center">
                        <img src="/images/logos/knocked_out_coin_w_rays.svg" className="block w-[37.5vw] lg:w-[30vw]" />
                    </div>
                </div>

                <h1 className="heading-display text-off-white text-center mb-3 max-w-sm md:max-w-lg lg:max-w-3xl xl:max-w-5xl">{t('pages.home.hero.title')}</h1>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.home.hero.subtitle')}
                </p>
            </header>

            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">
                {/* Hero Cards Row */}
                <div className="w-full grid grid-cols-1 md:grid-cols-3 gap-6 mt-10 mb-8 px-0">
                    {/* Think Agent Bundles Card */}
                    <a href="https://magiceden.us/collections/ethereum/******************************************" target="_blank" rel="noopener noreferrer" className="group rounded-xl p-[2px] transition-all duration-200">
                        <div className="relative min-h-[220px] flex flex-col items-start justify-center rounded-xl overflow-hidden border border-black-700 cursor-pointer transition-colors duration-200 w-full h-full">
                            <div className="absolute inset-0 transition-colors duration-200 bg-[#0D0D0D]/70 group-hover:bg-[#0D0D0D]/85" aria-hidden="true"></div>
                            <div className="relative z-10 flex flex-col items-start justify-center text-left py-6 px-8 w-full">
                                <div className="flex items-center mb-2 group">
                                    <h3 className="text-2xl font-bold text-white font-token group-hover:underline">Think Agent Bundles</h3>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="h-7 w-7 text-white group-hover:text-teal-400">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 6H19.5V12" />
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 13.5L19.5 6" />
                                    </svg>
                                </div>
                                <p className="font-mono text-gray-200 text-lg">The great unbundling begins this month</p>
                            </div>
                            <div className="absolute inset-0 -z-10" style={{backgroundImage: 'url(/images/agent-bundles.jpg)', backgroundSize: 'cover', backgroundPosition: 'center'}}></div>
                        </div>
                    </a>
                    {/* BASIIC Art Reveal Card */}
                    <a href="https://x.com/thinkagents/status/1942301042814615920" target="_blank" rel="noopener noreferrer" className="group rounded-xl p-[2px] transition-all duration-200">
                        <div className="relative min-h-[220px] flex flex-col items-start justify-center rounded-xl overflow-hidden border border-black-700 cursor-pointer transition-colors duration-200 w-full h-full">
                            <div className="absolute inset-0 transition-colors duration-200 bg-[#0D0D0D]/70 group-hover:bg-[#0D0D0D]/85" aria-hidden="true"></div>
                            <div className="relative z-10 flex flex-col items-start justify-center text-left py-6 px-8 w-full">
                                <div className="flex items-center mb-2 group">
                                    <h3 className="text-2xl font-bold text-white font-token group-hover:underline">BASIIC Art Reveal</h3>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="h-7 w-7 text-white group-hover:text-teal-400">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 6H19.5V12" />
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 13.5L19.5 6" />
                                    </svg>
                                </div>
                                <p className="font-mono text-gray-200 text-lg">View the teaser by the legendary artificial artist</p>
                            </div>
                            <div className="absolute inset-0 -z-10" style={{backgroundImage: 'url(/images/basiic-art-reveal.jpg)', backgroundSize: 'cover', backgroundPosition: 'center'}}></div>
                        </div>
                    </a>
                    {/* THINK Token Launch Card */}
                    <NavLink to="/claim" className="group rounded-xl p-[2px] transition-all duration-200">
                        <div className="relative min-h-[220px] flex flex-col items-start justify-center rounded-xl overflow-hidden border border-black-700 cursor-pointer transition-colors duration-200 w-full h-full">
                            <div className="absolute inset-0 transition-colors duration-200 bg-[#0D0D0D]/70 group-hover:bg-[#0D0D0D]/85" aria-hidden="true"></div>
                            <div className="relative z-10 flex flex-col items-start justify-center text-left py-6 px-8 w-full">
                                <div className="flex items-center mb-2 group">
                                    <h3 className="text-2xl font-bold text-white font-token group-hover:underline">THINK Token Launch</h3>
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor" className="h-7 w-7 text-white group-hover:text-teal-400">
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M13.5 6H19.5V12" />
                                        <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 13.5L19.5 6" />
                                    </svg>
                                </div>
                                <p className="font-mono text-gray-200 text-lg">Learn more about the THINK Token and why it matters</p>
                            </div>
                            <div className="absolute inset-0 -z-10" style={{backgroundImage: 'url(/images/think-token-launch.jpg)', backgroundSize: 'cover', backgroundPosition: 'center'}}></div>
                        </div>
                    </NavLink>
                </div>


                {/* Video with Gradient border */}
                <div className="mt-12 mb-12 w-full flex flex-col justify-center items-center" id="about">
                    <div className="w-full linear-gradient-sand-to-teal h-[5px]"></div>
                    <div className="w-full flex flex-row items-stretch">
                        <div className="vertical-gradient-sand-to-teal w-[5px]"></div>
                        <div id="video-container" className="w-[calc(100%-10px)] h-full relative">
                            <img src="/images/youtube-borderless-2.jpg" id='video-thumbnail' style={showVideo ? { display: 'none' } : {}} className="w-full h-full absolute object-cover z-10" />
                            <button id="play-button"
                                style={showVideo ? { display: 'none' } : {}}
                                className="absolute group inset-0 z-20 flex items-center justify-center my-auto transition-colors duration-300 ease-in-out hover:bg-teal/20" onClick={toggleVideo}>
                                <img src="/images/homepage/play-button.svg" id='video-thumbnail' className="'w-1/3 h-1/3  transition-colors duration-300 ease-in-out group-hover:text-teal/20" />
                            </button>

                            <YouTube
                                style={showVideo ? { zIndex: 10 } : {}}
                                iframeClassName="w-full h-full aspect-[16/9] inset-0 z-[-1]"
                                videoId="CI430d6Q0H4"
                                opts={options}
                                onReady={onReady}
                                onPause={onPuase}
                                id="video"
                            />

                        </div>
                        <div className="vertical-gradient-teal-to-sand w-[5px]"></div>
                    </div>
                    <div className="w-full linear-gradient-teal-to-sand h-[5px]"></div>
                </div>

                {/* Connect to open-source section with icon */}
                <div className="w-full max-w-7xl mx-auto px-4 lg:px-8">
                    <div className="flex flex-col lg:flex-row items-center gap-8 lg:gap-12 mt-16 mb-16">
                        <div className="w-full lg:basis-[65%]">
                            <h1 className="text-4xl font-bold text-off-white font-token mb-6 text-left">
                                {t('pages.home.features.subtitle')}
                            </h1>
                            <p className="font-mono text-gray-400 text-left leading-relaxed mb-4">
                                {t('pages.home.features.description')}
                            </p>
                            <p className="font-mono text-gray-400 text-left leading-relaxed">
                                {t('pages.home.features.description2')}
                            </p>
                        </div>
                        <div className="w-full flex justify-center lg:justify-end lg:basis-[35%]">
                            <img src="/images/smiley-framework.svg" alt="Smiley Framework" className="w-64 h-64 lg:w-80 lg:h-80" />
                        </div>
                    </div>
                </div>


                {/* Think Benefits Section */}
                <div className="relative w-full pb-24 min-h-[500px] flex flex-col justify-center items-center overflow-visible">

                    <div className="text-off-white w-full mx-auto px-4 pt-12 md:pt-16 lg:px-16">
                        <header className="flex flex-row justify-center items-center gap-6 mb-16">
                            <div className="w-full h-[2px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                            <h2 className="font-token uppercase text-3xl lg:text-4xl xl:text-5xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent whitespace-nowrap">{t('pages.home.features.title')}</h2>
                            <div className="w-full h-[2px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                        </header>
                    </div>

                    <div className="w-full max-w-7xl mx-auto px-4 lg:px-8">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-20">

                            <div>
                                <h3 className="text-2xl font-bold text-off-white font-token mb-4">{t('pages.home.features.professionalAgents.title')}</h3>
                                <p className="font-mono text-gray-400 leading-relaxed">{t('pages.home.features.professionalAgents.description')}</p>
                            </div>

                            <div>
                                <h3 className="text-2xl font-bold text-off-white font-token mb-4">{t('pages.home.features.ownExperience.title')}</h3>
                                <p className="font-mono text-gray-400 leading-relaxed">{t('pages.home.features.ownExperience.description')}</p>
                            </div>

                            <div>
                                <h3 className="text-2xl font-bold text-off-white font-token mb-4">{t('pages.home.features.patentProtection.title')}</h3>
                                <p className="font-mono text-gray-400 leading-relaxed">{t('pages.home.features.patentProtection.description')}</p>
                            </div>

                            <div>
                                <h3 className="text-2xl font-bold text-off-white font-token mb-4">{t('pages.home.features.seamlessInteroperability.title')}</h3>
                                <p className="font-mono text-gray-400 leading-relaxed">{t('pages.home.features.seamlessInteroperability.description')}</p>
                            </div>

                        </div>
                    </div>
                </div>
            </PrimaryContainerComponent>


            <div className="w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}
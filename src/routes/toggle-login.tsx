import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { IconChevronDown } from "@/components/ui/IconChevronDown";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { useNavigate } from "react-router";
import { useAuth } from "@/components/auth/AuthContext";
import { useEffect } from "react";

export default function ToggleLogin() {
    const { canLogin, assignCanLogin } = useAuth();
    const navigate = useNavigate();

    useEffect(() => {
        console.log(canLogin)
        assignCanLogin(!canLogin);
        navigate("/");
        window.location.reload();
    }, [])

    return (   
        null
    );
}
import React, { useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';

const FVLogout: React.FC = () => {
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const code = searchParams.get('code');
    const state = searchParams.get('state');
    const iss = searchParams.get('iss');

    if (code && state && iss) {
      console.log('Code:', code);
      console.log('State:', state);
      console.log('ISS:', iss);
      // TODO: Implement OAuth2 login logic here using code, state, and iss
    } else {
      console.log('Missing required query parameters for login.');
      // TODO: Handle missing parameters, e.g., redirect to an error page or back to initial login step
    }
  }, [searchParams]);

  return (
    <div>
      <h1>Futureverse Logout</h1>
      <p>Processing logout...</p>
      {/* You can add a loading spinner or message here */}
    </div>
  );
};

export default FVLogout;

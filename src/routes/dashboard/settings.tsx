import { useNavigate } from "react-router";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { useQuery } from "@tanstack/react-query";
import { apiClient, isGetNFTResponse, isGetWalletResponse, web3Client } from "@/lib/api";
import { WalletButton } from "@/components/ui/wallet-button";
import { useEffect, useState } from "react";
import { useUser } from "@account-kit/react";
import { useAuth } from "@/components/auth/AuthContext";
import { NetworkNfts, OwnedNFTs } from "@/types";
import { IconChevronDown } from "@/components/ui/IconChevronDown";
import { Button } from "@/components/ui/button";

export default function DashboardSettings() {
    const { user, authLogin } = useAuth();
    const accountUser = useUser();
    const [isLoading, setIsLoading] = useState(true);
    const [openedIds, setOpenedIds] = useState<string[]>(user?.address ? [user.address.toLowerCase()] : []);


    function toggleOpened(event: any, id: string) {
        event.preventDefault();
        if (openedIds.includes(id)) {
            setOpenedIds(openedIds.filter((i) => i !== id));
        } else {
            setOpenedIds([...openedIds, id]);
        }
    }

    let query = useQuery({
        queryKey: ["dashboard-settings"],
        queryFn: async () => {
            const walletResponse = await apiClient.getWallets();
            if (isGetWalletResponse(walletResponse)) {
                const collectionPromises = walletResponse.wallets.flatMap(async (wallet) => {
                    const walletNfts = await web3Client.getWalletNFTs(wallet.address);
                    if (isGetNFTResponse(walletNfts)) {
                        return {
                            wallet: wallet,
                            networks: walletNfts.allNfts
                        };
                    }
                    return {
                        wallet: wallet,
                        networks: {
                            ownedNfts: [],
                            totalCount: 0
                        } as OwnedNFTs
                    };
                });
                const allCollections = await Promise.all(collectionPromises);
                setIsLoading(false);
                return {
                    wallets: walletResponse.wallets,
                    allCollections
                };
            }
            return null;
        },
        retry: 1,
        refetchOnWindowFocus: "always",
    });

    useEffect(() => {

        if (accountUser && user?.address !== accountUser.address) {
            console.log("add new wallet", accountUser?.address);
            apiClient.addWallet(accountUser.address).then(() => {
                query.refetch();
                authLogin({ address: accountUser.address });
                window.location.reload();
                
            });
        }
    }, [accountUser]);

    const removeWallet = (e: any, walletId: string) => {
        e.preventDefault();
        apiClient.removeWallet(walletId).then(() => {
            query.refetch();
        });
    }

    const setPrimaryWallet = (e: any, walletId: string) => {
        e.preventDefault();
        apiClient.setPrimaryWallet(walletId).then(() => {
            query.refetch();
        });
    }

    const wallets = query && query.data && query.data.wallets
    const collections = query && query.data && query.data.allCollections


    if (isLoading) {
        return (
            <DashboardLayout> 
                <div className="w-full py-16 px-4 lg:px-8 xl:px-10 2xl:px-16 3xl:px-24 min-h-[600px] bg-gradient-to-b from-teal to-black ">
                <h1 className="text-8xl text-white font-light"> Settings</h1>
                    <div>Loading...</div>
                </div>
            </DashboardLayout>
        )
    }
    return (
        <DashboardLayout> 
            <div className="relative w-full py-16 px-4 lg:px-8 xl:px-10 2xl:px-16 3xl:px-24 min-h-[600px] bg-gradient-to-b from-teal to-black ">
            <div className="absolute top-0 right-0 h-full overflow-hidden z-0 m-5">
                <WalletButton addWallet={true} />
                </div>
                <h1 className="text-8xl text-white font-light"> Settings</h1>
                {/* TODO REMOVE ME! */}
                <button onClick={() => {throw new Error("This is your first error!");}}>Break the world</button>

                <div className="mt-10">
                    <br />
                    <br />
                    <h2 className="pt-4 uppercase text-2xl font-bold text-off-white font-token text-center mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">Wallets</h2>
                    <section id="faq" className="my-12">
                        <div className="st-accordion" data-controller="accordion">
                            {wallets && wallets.map((wallet: any) => {
                                let isOpen = openedIds.includes(wallet.address);
                                return (
                                    <div>
                                        <div>
                                            <div className={`st-accordion__icon flex flex-row justify-between py-6 cursor-pointer ${isOpen ? 'st-accordion__icon--opened' : ''}`} onClick={(e) => toggleOpened(e, wallet.address.toLowerCase())}>
                                                <div className="font-token text-off-white text-2xl flex-grow">
                                                    {wallet.address}
                                                    { wallet.primary ? " (Primary)" : null }
                                                </div>
                                                <span className="flex flex-direction row justify-end items-start w-10 flex-shrink">
                                                    <IconChevronDown className="w-6 text-off-white" />
                                                </span>
                                            </div>
                                        </div>
                                        <div className={`st-accordion__content border-b border-black-700 font-mono text-off-white ${isOpen ? 'st-accordion__content--visible' : ''}`}>
                                            <div className="py-6 px-2">
                                                {collections && collections.filter((collectionItem: any) => collectionItem.wallet.address === wallet.address).map((collectionItem: any) => {
                                                    return (
                                                        <div key={`collectionItem-${collectionItem.wallet.address}`}>
                                                            <Button variant={"outline"} className="ml-0 mr-2" onClick={(e, ) => setPrimaryWallet(e, wallet.id)}>{ wallet.primary ? " Primary" : " Set Primary" }</Button>
                                                            <Button variant={"outline"} className="mx-2" onClick={(e) => e.preventDefault()}>Remove Wallet</Button>
                                                            <br />
                                                            <br />
                                                            {collectionItem.networks && collectionItem.networks.map((networkNfts: NetworkNfts ) => {
                                                                console.log(networkNfts)
                                                                return (
                                                                    <div key={`networkNfts-${networkNfts.network}`}>
                                                                        <div  className="border-b border-white-700 py-2">
                                                                            {networkNfts.network}
                                                                        </div>
                                                                        {networkNfts.nfts.ownedNfts && networkNfts.nfts.ownedNfts.length === 0 && (
                                                                        
                                                                            <div className="mt-10 relative w-full text-center">
                                                                                No Collections
                                                                            </div>
                                                                        )}
                                                                        <div className="mt-10 md:columns-2 lg:grid lg:grid-cols-4 xl:grid-cols-5 w-full">
                                                                            {networkNfts.nfts.ownedNfts && networkNfts.nfts.ownedNfts.filter((collection: any) => collection.name ).map((collection: any, index: number) => {
                                                                                let imageUrl = null;
                                                                                if (collection.image && collection.image.pngUrl) {
                                                                                    imageUrl = collection.image.pngUrl;
                                                                                } else if (collection.image && collection.image.originalUrl) {
                                                                                    imageUrl = collection.image.originalUrl;
                                                                                }
                                                                                return (
                                                                                    <div key={`collectionItem-${collectionItem.wallet.address}-${index}`} className="relative group px-4 lg:px-6 overflow-hidden mb-3 lg:overflow-visible cursor-default">
                                                                                        <div className="relative w-full pb-[100%] z-0">
                                                                                            { imageUrl && <img src={`${imageUrl}`} className="absolute block w-full h-full inset-0 object-fill z-10" /> }
                                                                                        </div>
                                                                                        <div className="text-center">
                                                                                            {collection.contract.name ? `${collection.contract.name} : ${collection.name}` : collection.name}
                                                                                        </div>
                                                                                    </div>
                                                                                );
                                                                            })}
                                                                        </div>
                                                                    </div>
                                                                );
                                                            })}
                                                        </div>
                                                    );
                                                })}
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    </section>
                    
                </div>
            </div>
        </DashboardLayout>
    );
}
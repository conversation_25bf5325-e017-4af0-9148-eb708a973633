import { useAuth } from "@/components/auth/AuthContext";
import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { IconChevronDown } from "@/components/ui/IconChevronDown";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { useNavigate } from "react-router";
import { DashboardLayout } from "@/components/layouts/dashboard-layout";
import { addAllowanceTransaction, getAstoDetails, mintTransaction } from "@/lib/swap/asto";
import { alchemy } from "@/lib/web3Config";
import { sepolia, mainnet } from "@account-kit/infra";
import { User } from "@/types";
import { useQuery } from "@tanstack/react-query";
import { getThinkDetails, getThinkTokenContract } from "@/lib/swap/think";
import { Button } from "@/components/ui/button";
import { getSwapDetails, swapTokenTx } from "@/lib/swap/swap";
import Big from 'big.js';
import { useSendUserOperation, useSmartAccountClient } from "@account-kit/react";
import { useState } from "react";
import env from "@/environments/config";
import { calculateAstoThink } from "@/components/ThinkTokenCard/TokenFunctions";

export default function Token() {
    const { user } = useAuth();
    const { client } = useSmartAccountClient({ type: "LightAccount" });
    const [transactionsList, setTransactionsList] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const amountToSwap = 200;
    const amountToMint = 500;

    console.log("client", client);

    const { sendUserOperation, isSendingUserOperation } = useSendUserOperation({
        client,
        // optional parameter that will wait for the transaction to be mined before returning
        waitForTxn: true,
        onSuccess: ({ hash, request }) => {
            // [optional] Do something with the hash and request
            console.log(hash);
            console.log(request);
            setTransactionsList([...transactionsList, hash]);
        },
        onError: (error) => {
            // [optional] Do something with the error
            console.error(error)
        },
    });

    let quantities = useQuery({
        queryKey: ["token-details"],
        queryFn: async () => {
            const result = {
                asto: await getAstoDetails(user),
                think: await getThinkDetails(user),
                swapper: await getSwapDetails()
            };
            setIsLoading(false);
            return result;
        },
        retry: 1,
        refetchOnWindowFocus: "always",
    });

    const getAsto = (e: any) => {
        e.preventDefault();
        if (user) {
            // TODO read decimals from contract (currently it is 18)
            const mintTx = mintTransaction(new Big(amountToMint).mul(10 ** 18).toString(), user);
            sendUserOperation({
                uo: {
                    ...mintTx
                }
            });
        }
    }

    const swapAstoTokens = (e: any, amount: number = amountToSwap) => {
        e.preventDefault();
        const bigAmount = new Big(amount).mul(10 ** 18).toString()
        // TODO read decimals from contract (currently it is 18)
        const addAllowanceTx = addAllowanceTransaction(bigAmount);
        sendUserOperation({
            uo: {
                ...addAllowanceTx
            }
        });
        const swapTx = swapTokenTx(bigAmount);
        sendUserOperation({
            uo: {
                ...swapTx
            }
        });
    }

    if (isLoading) {
        return (
            <DashboardLayout>
                <div className="w-full py-16 px-4 lg:px-8 xl:px-10 2xl:px-16 3xl:px-24 min-h-[600px] bg-gradient-to-b from-teal to-black ">
                    <h1 className="text-xl md:text-2xl lg:text-8xl text-white font-light"> Token</h1>
                    <div>Loading...</div>
                </div>
            </DashboardLayout>
        )
    }

    const thinkTokenData = quantities && quantities.data && quantities.data.think;
    const astoTokenData = quantities && quantities.data && quantities.data.asto;
    const swapperData = quantities && quantities.data && quantities.data.swapper;
    const astoDecimals = astoTokenData && astoTokenData.decimals;
    const swappableThink = astoTokenData &&
        thinkTokenData &&
        astoTokenData.balance &&
        astoTokenData.totalSupply &&
        thinkTokenData.totalSupply &&
        astoTokenData.totalSupply !== "0" &&
        thinkTokenData.totalSupply !== "0"
        ? calculateAstoThink(`${astoTokenData.balance}`, astoTokenData.totalSupply, thinkTokenData.totalSupply)
        : "0";
    const swappable100 = astoTokenData &&
        thinkTokenData &&
        astoTokenData.totalSupply &&
        thinkTokenData.totalSupply &&
        astoTokenData.totalSupply !== "0" &&
        thinkTokenData.totalSupply !== "0"
        ? calculateAstoThink(`${amountToSwap}`, astoTokenData.totalSupply, thinkTokenData.totalSupply)
        : "0";

    return (
        <DashboardLayout>
            <div className="relative w-full py-16 px-4 lg:px-8 xl:px-10 2xl:px-16 3xl:px-24 min-h-[600px] bg-gradient-to-b from-teal to-black ">
                <div className="absolute top-0 right-0 h-full overflow-hidden z-0 m-5">
                    {astoTokenData ? <Button className="mx-2" variant={"outline"} onClick={getAsto}>Get 500 ${astoTokenData.symbol}</Button> : null}
                    <Button variant={"outline"} onClick={() => quantities.refetch()}>Reload</Button>
                </div>
                <h1 className="text-xl md:text-2xl lg:text-8xl text-white font-light"> Token</h1>
                <br />
                <div>
                    <h2 className="underline">{thinkTokenData && thinkTokenData.tokenName}</h2>
                    <h2 className="">Token Symbol: {thinkTokenData && `$${thinkTokenData.symbol}`}</h2>
                    <h2 className="">Token Balance: {thinkTokenData && thinkTokenData.balance.toFixed(4)}</h2>
                    <h2 className="">Token Decimals: {thinkTokenData && thinkTokenData.decimals}</h2>
                    <h2 className="">Token Stake Allowance: {thinkTokenData && thinkTokenData.stakeAllowance}</h2>
                    <h2 className="">Token Total Supply: {thinkTokenData && thinkTokenData.totalSupply}</h2>
                    <br />
                </div>
                {
                    astoTokenData &&
                    (<div className="flex flex-row align-middle w-full">
                        <div className="flex flex-col flex-1">
                            <h2 className="underline">{astoTokenData && astoTokenData.tokenName}</h2>
                            <h2 className="">Token Symbol: {astoTokenData && `$${astoTokenData.symbol}`}</h2>
                            <h2 className="">Token Balance: {astoTokenData && astoTokenData.balance.toFixed(4)}</h2>
                            <h2 className="">Token Allowance: {astoTokenData && astoTokenData.allowanceForSwap}</h2>
                            <h2 className="">Token Decimals: {astoTokenData && astoTokenData.decimals}</h2>
                            <h2 className="">Token Total Supply: {astoTokenData && astoTokenData.totalSupply}</h2>
                            <br />
                            <br />
                        </div>
                        <div className="flex flex-col align-middle justify-center lex-1">
                            <h2 className="">THINK Address: {swapperData && swapperData.thinkAddress}</h2>
                            <h2 className="">ASTO Address: {swapperData && swapperData.astoAddress}</h2>
                            <h2 className="">Is Paused: {swapperData && swapperData.paused ? "Yes" : "No"}</h2>
                            <h2 className="">THINK Swappable Amount: {swappableThink}</h2>
                            <br />
                            <br />
                            <div className="flex flex-row align-middle justify-center text-center">
                                {isSendingUserOperation ?
                                    <Button variant={"outline"} disabled>Swapping...</Button> :
                                    <Button variant={"outline"} onClick={swapAstoTokens}>Swap {amountToSwap} $ASTO to {swappable100} THINK</Button>
                                }
                            </div>
                        </div>
                    </div>
                    )
                }
                <br />
                {transactionsList.length > 0 &&
                    <div>
                        Transactions:
                        <ul className="list-disc ml-5">
                            {transactionsList.map((tx: string, index: number) => {
                                if (env.PRIMARY_CHAIN === sepolia) {
                                    return <li key={index}><a href={`https://sepolia.etherscan.io/tx/${tx}`} target="_blank">{tx}</a></li>;
                                } else if (env.PRIMARY_CHAIN === mainnet) {
                                    return <li key={index}><a href={`https://etherscan.io/tx/${tx}`} target="_blank">{tx}</a></li>;
                                }
                            })}
                        </ul>
                    </div>
                }
            </div>
        </DashboardLayout>
    );
}
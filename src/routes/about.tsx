import { HalfSunRays } from "@/components/ui/HalfSunRays";
import { PrimaryContainerComponent } from "@/components/layouts/PrimaryContainerComponent";
import { SubFooter } from "@/components/layouts/SubFooter";
import { apiClient } from "@/lib/api";
import { TeamMember } from "@/types";
import { useQuery } from "@tanstack/react-query";
import React from "react";
import env from "@/environments/config";
import { IconX } from "@/components/ui/IconX";
import { IconGithub } from "@/components/ui/IconGithub";
import { IconLinkedin } from "@/components/ui/IconLinkedin";
import { IconInstagram } from "@/components/ui/IconInstagram";
import { useTranslation } from "@/hooks/useTranslation";

export default function About() {
    const { t, currentLanguage } = useTranslation();
    const query = useQuery({
        queryKey: ["team-members"],
        queryFn: async () => {
            const data = await apiClient.getTeamMembers(currentLanguage);
            return data;
        },
        retry: 1,
        refetchOnWindowFocus: "always",
    });
    return (
        <div>
            <div className="mb-8 text-off-white w-full mx-auto px-4 pt-8 md:pt-12 lg:pt-20 md:px-16 lg:px-16">
                <header className="flex flex-row justify-center items-center gap-4 ">
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                    <h2 className="text-6xl md:text-6xl lg:text-9xl font-token">{t('pages.about.title')}</h2>
                    <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                </header>
            </div>
            <PrimaryContainerComponent className="flex flex-col justify-center items-center pb-0">

                <h1 className="pt-4 uppercase text-4xl font-bold text-off-white font-token text-center mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.about.hero.title')}
                </h1>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.about.hero.subtitle')}
                </p>

                <h2 className="pt-4 uppercase text-2xl font-bold text-off-white font-token text-center mb-3 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.about.hero.mission')}
                </h2>


                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.about.hero.mission2')}
                </p>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.about.description.paragraph1')}
                </p>

                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.about.description.paragraph2')}
                </p>


                <p className="mt-4 text-center font-mono text-gray-400 max-w-md md:max-w-2xl lg:max-w-4xl xl:max-w-5xl">
                    {t('pages.about.description.paragraph3')}
                </p>

                <div className="text-off-white w-full mx-auto px-4 pt-12 md:pt-16 lg:px-16">
                    <header className="flex flex-row justify-center items-center gap-4 ">
                        <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                        <h2 className="font-token uppercase text-2xl lg:text-3xl xl:text-6xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent lg:col-span-3 place-self-center">{t('pages.about.team.title')}</h2>
                        <div className="w-full h-[1px] bg-gradient-to-r from-[#DEAB46] to-[#F5E7BB]"></div>
                    </header>
                    </div>
                    <div className="w-full mx-auto lg:px-10">
                    <div className="mt-10 md:columns-1 lg:grid lg:grid-cols-2 xl:grid-cols-3 w-full">
                        {query && query.data && query.data['team_members'] && query.data['team_members'].map((member: TeamMember) => {
                            return (
                                <div key={member.name} className="relative group px-4 lg:px-6 overflow-hidden mb-3lg:overflow-visible cursor-default">
                                    <div className="relative w-full pb-[100%] z-0">
                                        { member.image && <img src={`${env.API_BASE_URL}${member.image}`} className="absolute block w-full h-full inset-0 object-fill z-10" /> }
                                    </div>

                                    <div className="min-h-full top-0 pt-10 pb-14 overflow-hidden">
                                        <h3 className="font-token uppercase text-2xl lg:text-2xl xl:text-5xl bg-gradient-to-b from-teal to-sand bg-clip-text text-transparent lg:col-span-3 no-hyphen-auto" lang="en">{member.name}</h3>
                                        { member.title && <h4 className="font-mono uppercase text-white text-xs lg:text-sm font-bold mb-4">{member.title}</h4> }
                                        <div className="font-mono text-gray-400">
                                        <div className="text-sm lg:text-base mb-8">
                                            {member.bio}
                                        </div>
                                        <div className="flex flex-row">
                                            {/* Social icons inherit bio color. Team members are loaded from a database, so this works for all. */}
                                            { member.url_x && 
                                                (<a href={member.url_x} className="inline-block mr-4 text-gray-400" target="_blank" rel="noopener noreferrer">
                                                    <IconX className="w-7 h-7" />
                                                </a>) 
                                            }
                                            { member.url_linkedin && 
                                                (<a href={member.url_linkedin} className="inline-block mr-4 text-gray-400" target="_blank" rel="noopener noreferrer">
                                                    <IconLinkedin className="w-7 h-7" />
                                                </a>) 
                                            }
                                            { member.url_github && 
                                                (<a href={member.url_github} className="inline-block mr-4 text-gray-400" target="_blank" rel="noopener noreferrer">
                                                    <IconGithub className="w-7 h-7" />
                                                </a>) 
                                            }
                                            { member.url_ig && 
                                                (<a href={member.url_ig} className="inline-block mr-4 text-gray-400" target="_blank" rel="noopener noreferrer">
                                                    <IconInstagram className="w-7 h-7" />
                                                </a>) 
                                            }
                                        </div>
                                        </div>
                                    </div>
                                </div>
                            )
                        })}
                    </div>
                </div>

            </PrimaryContainerComponent>
            <div className="w-full mx-auto border-b border-black-700">
                <HalfSunRays className="w-full mx-auto" />
            </div>

            <SubFooter />

        </div>
    );
}
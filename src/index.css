@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Node module style imports */
@import 'plyr/dist/plyr.css';
@import '@kanety/stimulus-accordion/dist/index.css';

/* Application styles */
@import './styles/toastify.css';
@import './styles/headings.css';
@import './styles/loading.css';
@import './styles/backgrounds.css';
@import './styles/plyr.css';
@import './styles/utils.css';


@layer base {
    :root {
        --plyr-font-family: 'ibm-plex-mono', 'monospace';
        --plyr-color-main: #cf0029;
        --plyr-color-main-light: #f4e6ba;
        --plyr-video-background: #0e0f12;
        --plyr-badge-background: #0e0f12;
        --plyr-badge-text-color: #f4e6ba;
        --plyr-range-thumb-background: #f4e6ba;
        --plyr-tooltip-background: #0e0f12;
        --plyr-tooltip-color: #f4e6ba;
        --plyr-audio-controls-background: #0e0f12;
        --plyr-video-control-color: #f4e6ba;
        --plyr-video-control-color-hover: #f4e6ba;
    }

    .dark {
        --background: 0 0% 9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 11.5%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 12%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 240 5.9% 10%;
        --secondary: 240 3.7% 15.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 240 3.7% 15.9%;
        --muted-foreground: 240 5% 64.9%;
        --accent: 240 3.7% 15.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;
        --ring: 240 4.9% 83.9%;
        --sidebar-background: 240 5.9% 10%;
        --sidebar-foreground: 240 4.8% 95.9%;
        --sidebar-primary: 224.3 76.3% 48%;
        --sidebar-primary-foreground: 0 0% 100%;
        --sidebar-accent: 240 3.7% 15.9%;
        --sidebar-accent-foreground: 240 4.8% 95.9%;
        --sidebar-border: 240 3.7% 15.9%;
        --sidebar-ring: 217.2 91.2% 59.8%;
    }
}

@layer base {
    * {
        @apply antialiased;
    }

    body {
        background-position: top center;
        background-repeat: no-repeat;
        background-size: 100%;
        @apply lg:bg-cover;
    }

    .scrollbar-style {
        /* add background color to match. May have a better matched color, but this matches exactly for now */
        scrollbar-color: #ffffff rgb(14 15 18 / var(--tw-bg-opacity, 1));
    }
}


.st-accordion a svg {
    transition: transform 0.3s;
    transform: rotate(-90deg);
}

.st-accordion a.st-accordion__icon--opened svg {
    transform: rotate(0deg);
}
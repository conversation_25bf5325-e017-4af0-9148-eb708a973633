import { AlchemyAccountsUIConfig, createConfig, cookieStorage } from "@account-kit/react";
import { sepolia, alchemy } from "@account-kit/infra";
import { QueryClient } from "@tanstack/react-query";
import env from "@/environments/config";
const uiConfig: AlchemyAccountsUIConfig = {
  illustrationStyle: "outline",
  auth: {
    sections: [
      [{ "type": "email", "emailMode": "otp" }],
      [{ "type": "external_wallets", "walletConnect": { "projectId": env.WALLET_CONNECT_PROJECT_ID ?? "" } }],
    ],
    addPasskeyOnSignup: false,
  },
};

export const config = createConfig({
  // if you don't want to leak api keys, you can proxy to a backend and set the rpcUrl instead here
  // get this from the app config you create at https://dashboard.alchemy.com/accounts?utm_source=demo_alchemy_com&utm_medium=referral&utm_campaign=demo_to_dashboard
  transport: alchemy({ apiKey: env.ACCOUNT_KIT_API_KEY ?? "" }),
  chain: env.PRIMARY_CHAIN,
  ssr: true,// set to false if you're not using server-side rendering
  storage: cookieStorage, // more about persisting state with cookies: https://accountkit.alchemy.com/react/ssr#persisting-the-account-state
  enablePopupOauth: true,
}, uiConfig);

export const queryClient = new QueryClient();
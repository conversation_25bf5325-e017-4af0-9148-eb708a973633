import { Alchemy, Network } from "alchemy-sdk";
import { createAlchemyWeb3 } from "@alch/alchemy-web3";
import { createPublicClient, http } from 'viem';
import { define<PERSON>hain } from 'viem';
import env from "../environments/config";
import { sepolia, mainnet, arbitrum, arbitrumSepolia } from "@account-kit/infra";


const getPrimaryNetwork = () => {
    if (env.PRIMARY_CHAIN === sepolia) {
        return Network.ETH_SEPOLIA;
    } else if (env.PRIMARY_CHAIN === mainnet) {
        return Network.ETH_MAINNET;
    } else if (env.PRIMARY_CHAIN === arbitrum) {
        return Network.ARB_MAINNET;
    } else if (env.PRIMARY_CHAIN === arbitrumSepolia) {
        return Network.ARB_SEPOLIA;
    } else {
        throw new Error("Primary chain not supported: " + env.PRIMARY_CHAIN);
    }
}

export const defaultNetwork = getPrimaryNetwork();

// Root Network chain definition (mainnet)
export const rootNetwork = defineChain({
  id: 7668,
  name: 'The Root Network',
  nativeCurrency: {
    decimals: 6,
    name: 'XRP',
    symbol: 'XRP',
  },
  rpcUrls: {
    default: {
      http: ['https://root.rootnet.live/archive'],
      webSocket: ['wss://root.rootnet.live/archive/ws'],
    },
  },
  blockExplorers: {
    default: { name: 'Root Network Explorer', url: 'https://explorer.rootnet.live' },
  },
  contracts: {
    multicall3: {
      address: '******************************************',
      blockCreated: 1,
    },
  },
});

// Porcini testnet chain definition (for development)
export const porciniTestnet = defineChain({
  id: 7672,
  name: 'Porcini Testnet',
  nativeCurrency: {
    decimals: 6,
    name: 'XRP',
    symbol: 'XRP',
  },
  rpcUrls: {
    default: {
      http: ['https://porcini.rootnet.app/archive'],
      webSocket: ['wss://porcini.rootnet.app/archive/ws'],
    },
  },
  blockExplorers: {
    default: { name: 'Porcini Rootscan', url: 'https://porcini.rootscan.io' },
  },
  testnet: true,
});

// Ethereum configuration (existing)
const settings = {
  apiKey: env.ACCOUNT_KIT_API_KEY, 
  network: defaultNetwork,
}

export const web3 = createAlchemyWeb3(env.ALCHEMY_KEY);
export const alchemy = new Alchemy(settings);

// Root Network configuration - use Porcini for development, mainnet for production
const rootChain = env.FV_ENVIRONMENT === 'development' ? porciniTestnet : rootNetwork;
const rootRpcUrl = env.FV_ENVIRONMENT === 'development'
  ? 'https://porcini.rootnet.app/archive'
  : 'https://root.rootnet.live/archive';

export const rootNetworkClient = createPublicClient({
  chain: rootChain,
  transport: http(rootRpcUrl),
});

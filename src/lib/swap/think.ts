import { defaultNetwork, web3 } from "../web3Config";
import { contractABI } from "./abi-think";
import { User } from "@/types";
import { getStakingVaultContractURL } from "../staking-vault/staking-vault";
import { Network } from "alchemy-sdk";
import { Address } from "viem";
import { calculateAmountFromDecimal } from "@/components/ThinkTokenCard/TokenFunctions";
import Big from "big.js";

export const THINK_CONTRACT_SEPOLIA_ADDRESS="******************************************"
export const THINK_CONTRACT_MAINNET_ADDRESS="0x0"

export function getThinkContractURL(networkName: Network = defaultNetwork): Address {
    switch(networkName) {
        case Network.ETH_SEPOLIA:
            return THINK_CONTRACT_SEPOLIA_ADDRESS;
        case Network.ETH_MAINNET:
            return THINK_CONTRACT_MAINNET_ADDRESS;
        default:
            throw new Error("Staking Vault contract not found for network: " + networkName);
    }
}

export function getThinkTokenContract(networkName: Network = defaultNetwork) {
    return new web3.eth.Contract(contractABI, getThinkContractURL(networkName));
}

export function addStakeAllowanceTransaction(amount: string) {
    const contract = getThinkTokenContract()
    const data = contract.methods.approve(getStakingVaultContractURL(), amount).encodeABI();
    const transactionParameters = {
        target: getThinkContractURL(), // Required except during contract publications.
        data: data,
    } as {
        target: `0x${string}`;
        data: `0x${string}`;
    };
    return transactionParameters;
}


export async function getThinkDetails(user: User | undefined | null) {
    const astoContract = getThinkTokenContract()
    const tokenName: string = await astoContract.methods.name().call();
    const symbol: string = await astoContract.methods.symbol().call();
    const decimals: string = await astoContract.methods.decimals().call();
    const balance: bigint = user &&  await astoContract.methods.balanceOf(user.address).call();
    const stakeAllowance: string = user && await astoContract.methods.allowance(user.address, getStakingVaultContractURL()).call();
    const totalSupply: string = await astoContract.methods.totalSupply().call();
    return {
        tokenName,
        balance: new Big(calculateAmountFromDecimal(balance, BigInt(decimals))),
        decimals,
        symbol,
        stakeAllowance,
        totalSupply
    };
}

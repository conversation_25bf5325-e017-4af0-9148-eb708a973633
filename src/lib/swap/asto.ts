import { ContractRunner, ethers } from "ethers";
import { contractABI } from "./abi-asto";
import { contractABI as swapperAB<PERSON> } from "./abi-swapper";
import { rootSwapperABI } from "./abi-root-swapper";
import { alchemy, web3, rootNetworkClient, defaultNetwork } from "../web3Config";
import { User } from "@/types";
import { useSmartAccountClient, useSendUserOperation } from "@account-kit/react";
import { getSwapContractURL } from "./swap";
import Big from 'big.js';
import env from "@/environments/config";
import { Address, encodeFunctionData } from 'viem';
import { calculateAmountFromDecimal } from "@/components/ThinkTokenCard/TokenFunctions";
import { Network } from "alchemy-sdk";

// Ethereum ASTO Contract
export const ASTO_CONTRACT_SEPOLIA_ADDRESS = "******************************************"
export const ASTO_CONTRACT_MAINNET_URL="0x0";

// Root Network ASTO Contract addresses
export const ROOT_ASTO_CONTRACT_ADDRESS = "******************************************" as const; // Root Network mainnet ASTO
export const PORCINI_SWAPPER_CONTRACT_ADDRESS = "******************************************" as const; // Porcini Swapper

export function getRootAstoTokenContract() {
    return new web3.eth.Contract(contractABI, ROOT_ASTO_CONTRACT_ADDRESS);
}

export function getAstoContractURL(networkName: Network | 'root' | 'porcini' = defaultNetwork): Address {
    switch(networkName) {
        case Network.ETH_SEPOLIA:
            return ASTO_CONTRACT_SEPOLIA_ADDRESS;
        case Network.ETH_MAINNET:
            return ASTO_CONTRACT_MAINNET_URL;
        case 'porcini':
            return PORCINI_SWAPPER_CONTRACT_ADDRESS;
        case 'root':
            return ROOT_ASTO_CONTRACT_ADDRESS;
        default:
            throw new Error("ASTO contract not found for network: " + networkName);
    }
}

export function getAstoTokenContract() {
    return new web3.eth.Contract(contractABI, getAstoContractURL());
}

export function addAllowanceTransaction(amount: string) {
    const astoContract = getAstoTokenContract()
    const data = astoContract.methods.approve(getSwapContractURL(), amount).encodeABI();
    const transactionParameters = {
        target: getAstoContractURL(), // Required except during contract publications.
        data: data,
    } as {
        target: `0x${string}`;
        data: `0x${string}`;
    };
    return transactionParameters;
}

export function mintTransaction(amount: string, user: User) {
    const astoContract = getAstoTokenContract()
    const data = astoContract.methods.mint(user.address, amount).encodeABI();
    const transactionParameters = {
        target: getAstoContractURL(), // Required except during contract publications.
        data: data,
    } as {
        target: `0x${string}`;
        data: `0x${string}`;
    };
    return transactionParameters;
}

export async function getAstoDetails(user: User | undefined | null) {
    const astoContract = getAstoTokenContract()
    const tokenName = await astoContract.methods.name().call();
    const symbol = await astoContract.methods.symbol().call();
    const decimals = await astoContract.methods.decimals().call();
    const balance = user && await astoContract.methods.balanceOf(user.address).call();
    const allowanceForSwap = user && await astoContract.methods.allowance(user.address, getSwapContractURL()).call();
    const totalSupply = await astoContract.methods.totalSupply().call();
    return {
        tokenName,
        balance: new Big(calculateAmountFromDecimal(balance, BigInt(decimals))),
        decimals,
        symbol,
        allowanceForSwap,
        totalSupply
    };
}

// Root Network ASTO functions
export async function getRootAstoDetails(userAddress: string | undefined | null) {
    if (!userAddress || !rootNetworkClient) {
        return {
            tokenName: "Altered State Token",
            balance: 0,
            decimals: 18,
            symbol: "ASTO",
            allowanceForSwap: null,
            totalSupply: "0"
        };
    }

    // For development (Porcini), use mock data since it's a Swapper contract, not ASTO token
    if (env.FV_ENVIRONMENT === 'development') {
        return getPorciniAstoDetails(userAddress);
    }

    // TODO: Implement Root Network mainnet ASTO reading when needed
    // For now, return mock data for all cases
    return getPorciniAstoDetails(userAddress);

    /* TODO: Fix viem typing issues and implement proper Root Network reading
    try {
        // Read contract data using viem for Root Network mainnet
        const [tokenName, symbol, decimals, balance, totalSupply] = await Promise.all([
            rootNetworkClient.readContract({
                address: ROOT_ASTO_CONTRACT_ADDRESS,
                abi: contractABI,
                functionName: 'name',
            }),
            rootNetworkClient.readContract({
                address: ROOT_ASTO_CONTRACT_ADDRESS,
                abi: contractABI,
                functionName: 'symbol',
            }),
            rootNetworkClient.readContract({
                address: ROOT_ASTO_CONTRACT_ADDRESS,
                abi: contractABI,
                functionName: 'decimals',
            }),
            rootNetworkClient.readContract({
                address: ROOT_ASTO_CONTRACT_ADDRESS,
                abi: contractABI,
                functionName: 'balanceOf',
                args: [userAddress as `0x${string}`],
            }),
            rootNetworkClient.readContract({
                address: ROOT_ASTO_CONTRACT_ADDRESS,
                abi: contractABI,
                functionName: 'totalSupply',
            }),
        ]);

        return {
            tokenName: tokenName as string,
            balance: Number(balance as bigint) / 10 ** (decimals as number),
            decimals: decimals as number,
            symbol: symbol as string,
            allowanceForSwap: null, // Not needed for Root Network yet
            totalSupply: (totalSupply as bigint).toString()
        };
    } catch (error) {
        console.error("Error fetching Root Network ASTO details:", error);
        return {
            tokenName: "Altered State Token",
            balance: 0,
            decimals: 18,
            symbol: "ASTO",
            allowanceForSwap: null,
            totalSupply: "0"
        };
    }
    */
}

// Porcini testnet ASTO details (using real contract data)
async function getPorciniAstoDetails(userAddress: string) {
    try {
        // Get the real swapper contract data
        const swapperData = await getRootSwapperDetails();

        console.log(`Porcini testnet: Using real contract data for ${userAddress}`);
        console.log("Swapper data:", swapperData);

        // For now, we'll use mock balance since we don't have user's actual ASTO balance on Porcini
        // In a real implementation, you'd fetch the user's actual ASTO balance from the ASTO token contract
        const mockBalance = 1000; // Mock ASTO balance for testing
        // const balance = await getRootAstoBalance(userAddress);
        // console.log("Balance:", balance);

        return {
            tokenName: "Altered State Token (Porcini)",
            balance: mockBalance,
            decimals: 18,
            symbol: "ASTO",
            allowanceForSwap: null,
            totalSupply: swapperData.totalAsto // Use real total ASTO from contract
        };
    } catch (error) {
        console.error("Error fetching Porcini ASTO details:", error);
        return {
            tokenName: "Altered State Token (Porcini)",
            balance: 0,
            decimals: 18,
            symbol: "ASTO",
            allowanceForSwap: null,
            totalSupply: "0"
        };
    }
}

// Function to get Root Network swapper contract data
export async function getRootSwapperDetails() {
    if (!rootNetworkClient) {
        return {
            totalAstoScaled: "0",
            totalAsto: "0",
            totalThink: "0",
            rate: "0",
            scaledRate: "0",
            scale: "0",
            paused: false
        };
    }

    try {
        const [totalAstoScaled, totalAsto, totalThink, rate, scaledRate, scale, paused] = await Promise.all([
            rootNetworkClient.readContract({
                address: PORCINI_SWAPPER_CONTRACT_ADDRESS,
                abi: rootSwapperABI,
                functionName: 'SCALED_TOTAL_ASTO',
            }),
            rootNetworkClient.readContract({
                address: PORCINI_SWAPPER_CONTRACT_ADDRESS,
                abi: rootSwapperABI,
                functionName: 'TOTAL_ASTO',
            }),
            rootNetworkClient.readContract({
                address: PORCINI_SWAPPER_CONTRACT_ADDRESS,
                abi: rootSwapperABI,
                functionName: 'TOTAL_THINK',
            }),
            rootNetworkClient.readContract({
                address: PORCINI_SWAPPER_CONTRACT_ADDRESS,
                abi: rootSwapperABI,
                functionName: 'RATE',
            }),
            rootNetworkClient.readContract({
                address: PORCINI_SWAPPER_CONTRACT_ADDRESS,
                abi: rootSwapperABI,
                functionName: 'SCALED_RATE',
            }),
            rootNetworkClient.readContract({
                address: PORCINI_SWAPPER_CONTRACT_ADDRESS,
                abi: rootSwapperABI,
                functionName: 'SCALE',
            }),
            rootNetworkClient.readContract({
                address: PORCINI_SWAPPER_CONTRACT_ADDRESS,
                abi: rootSwapperABI,
                functionName: 'paused',
            }),
        ]);

        return {
            totalAstoScaled: (totalAstoScaled as bigint).toString(),
            totalAsto: (totalAsto as bigint).toString(),
            totalThink: (totalThink as bigint).toString(),
            rate: (rate as bigint).toString(),
            scaledRate: (scaledRate as bigint).toString(),
            scale: (scale as bigint).toString(),
            paused: paused as boolean
        };
    } catch (error) {
        console.error("Error fetching Root Network swapper details:", error);
        return {
            totalAstoScaled: "0",
            totalAsto: "0",
            totalThink: "0",
            rate: "0",
            scaledRate: "0",
            scale: "0",
            paused: false
        };
    }
}

// Function to create a Root Network swap transaction data
export function createRootNetworkSwapTransaction(astoAmount: string) {
    // Convert the amount to wei if needed (for 18 decimal ASTO)
    const amountInWei = new Big(astoAmount).times(new Big(10).pow(18)).toFixed(0);

    return {
        to: PORCINI_SWAPPER_CONTRACT_ADDRESS,
        data: encodeFunctionData({
            abi: rootSwapperABI,
            functionName: 'swap',
            args: [BigInt(amountInWei)]
        }),
        value: BigInt(0)
    };
}

// lets write a function to get the ASTO on a wallet for the root network
export async function getRootAstoBalance(userAddress: string) {
    if (!rootNetworkClient) {
        return 0;
    }

    const astoContract = getRootAstoTokenContract();
    const balance = await astoContract.methods.balanceOf(userAddress).call();
    return balance;
}
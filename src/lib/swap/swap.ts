import { User } from "@/types";
import { contractABI } from "./abi-swapper"
import { ethers } from "ethers";
import { defaultNetwork, web3 } from "../web3Config";
import { Network } from "alchemy-sdk";
import { Address } from "viem";

export const SWAP_CONTRACT_SEPOLIA_URL="******************************************";
export const SWAP_CONTRACT_MAINNET_URL="0x0";


export function getSwapContractURL(networkName: Network = defaultNetwork): Address {
    switch(networkName) {
        case Network.ETH_SEPOLIA:
            return SWAP_CONTRACT_SEPOLIA_URL;
        case Network.ETH_MAINNET:
            return SWAP_CONTRACT_MAINNET_URL;
        default:
            throw new Error("Staking Vault contract not found for network: " + networkName);
    }
}

export function getSwapperTokenContract(networkName: Network = defaultNetwork) {
    return new web3.eth.Contract(contractABI, getSwapContractURL(networkName));
}

export async function getSwapDetails() {
    const swapContract = getSwapperTokenContract()
    const astoAddress = await swapContract.methods.ASTO().call();
    const thinkAddress = await swapContract.methods.THINK().call();
    const paused = await swapContract.methods.paused().call();
    return {
        astoAddress,
        thinkAddress,
        paused
    };
}

export function swapTokenTx(amount: string) {
    const swapContract = getSwapperTokenContract();
    const data = swapContract.methods.swap(amount).encodeABI();
    const transactionParameters = {
        target: getSwapContractURL(), // Required except during contract publications.
        data: data,
    } as {
        target: `0x${string}`;
        data: `0x${string}`;
    };
    return transactionParameters;
}
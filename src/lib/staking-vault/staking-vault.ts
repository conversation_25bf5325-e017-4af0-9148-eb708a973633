import { Address } from "viem";
import { defaultNetwork, web3 } from "../web3Config";
import { contractABI } from "./abi-stakingvault"
import { Network } from "alchemy-sdk";

export const STAKING_VAULT_CONTRACT_SEPOLIA_URL="******************************************"
export const STAKING_VAULT_CONTRACT_MAINNET_URL="0x0"

export function getStakingVaultContractURL(networkName: Network = defaultNetwork): Address {
    switch(networkName) {
        case Network.ETH_SEPOLIA:
            return STAKING_VAULT_CONTRACT_SEPOLIA_URL;
        case Network.ETH_MAINNET:
            return STAKING_VAULT_CONTRACT_MAINNET_URL;
        default:
            throw new Error("Staking Vault contract not found for network: " + networkName);
    }
}

export function getStakingVaultContract(networkName: Network = defaultNetwork) {
    let contractAddress = getStakingVaultContractURL(networkName);
    return new web3.eth.Contract(contractABI, contractAddress);
}

export async function isPaused(): Promise<boolean> {
    const claimContract = getStakingVaultContract();
    return await claimContract.methods.paused().call();
}

export function stake(amount: string, timeLock: string) {
    const contract = getStakingVaultContract();
    const data = contract.methods.stake(amount, timeLock).encodeABI();
    const transactionParameters = {
        target: getStakingVaultContractURL(),
        data: data,
    } as {
        target: Address;
        data: `0x${string}`;
    };
    return transactionParameters;
}

export function unstake(stakeId: string) {
    const contract = getStakingVaultContract();
    const data = contract.methods.unstake(stakeId).encodeABI();
    const transactionParameters = {
        target: getStakingVaultContractURL(),
        data: data,
    } as {
        target: Address;
        data: `0x${string}`;
    };
    return transactionParameters;
}


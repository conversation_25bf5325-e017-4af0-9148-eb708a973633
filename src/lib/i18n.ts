import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enTranslations from '../locales/en.json';
import esTranslations from '../locales/es.json';
import zhTranslations from '../locales/zh.json';

const resources = {
  en: {
    translation: enTranslations,
  },
  es: {
    translation: esTranslations,
  },
  zh: {
    translation: zhTranslations,
  },
};

export const i18nInit = () => {
  console.log("initializing localization");
  
  i18n
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
      resources,
      lng: 'en', // default language
      fallbackLng: 'en',
      interpolation: {
        escapeValue: false, // React already escapes values
      },
      
      // Optional: Add namespace support
      // defaultNS: 'translation',
      
      // Optional: Add debugging in development
      debug: import.meta.env.DEV,
      
      // Optional: Add key separator and namespace separator
      // keySeparator: '.',
      // nsSeparator: ':',
      detection: {
        order: ['path', 'localStorage', 'navigator', 'htmlTag'],
        caches: ['localStorage'],
      },
    });
}

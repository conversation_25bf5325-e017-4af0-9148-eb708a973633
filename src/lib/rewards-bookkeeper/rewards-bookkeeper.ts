import { Address } from "viem";
import { defaultNetwork, web3 } from "../web3Config";
import { contractABI } from "./abi-rewards-bookkeeper"
import { Network } from "alchemy-sdk";
import { User } from "@/types";


export const REWARDS_BOOKKEEPER_CONTRACT_SEPOLIA_URL="******************************************"
export const REWARDS_BOOKKEEPER_CONTRACT_MAINNET_URL="0x0"

export function getRewardsBookkeeperContractURL(networkName: Network = defaultNetwork): Address {
    switch(networkName) {
        case Network.ETH_SEPOLIA:
            return REWARDS_BOOKKEEPER_CONTRACT_SEPOLIA_URL;
        case Network.ETH_MAINNET:
            return REWARDS_BOOKKEEPER_CONTRACT_MAINNET_URL;
        default:
            throw new Error("Rewards Bookkeeper contract not found for network: " + networkName);
    }
}

export function getRewardsBookkeeperContract(networkName: Network = defaultNetwork) {
    let contractAddress = getRewardsBookkeeperContractURL(networkName);
    return new web3.eth.Contract(contractABI, contractAddress);
}

export async function getTotalRewardsFromStakers(addresses: Address[]) {
    if (!addresses.length) return BigInt(0);
    const contract = getRewardsBookkeeperContract();
    const totalRewardsPerAddress = addresses.map(async (address) => {
        const totalRewards = await contract.methods.getTotalRewards(address).call();
        return BigInt(totalRewards);
    });
    const totalRewards = await Promise.all(totalRewardsPerAddress);
    return totalRewards.reduce((acc, curr) => acc + curr, BigInt(0));
}

export async function getRewardsDetails(user: User | undefined | null) {
    if (!user || !user.address) return null;
    const contract = getRewardsBookkeeperContract()
    const userTotalRewards = await contract.methods.getTotalRewards(user.address).call();
    const rewardsCount = await contract.methods.getRewardsCount(user.address).call();
    return {
        userTotalRewards: BigInt(userTotalRewards),
        rewardsCount
    };
}

import { Address } from "viem";
import { defaultNetwork, web3 } from "../web3Config";
import { contractABI } from "./abi-staking-bookkeeper"
import { Network } from "alchemy-sdk";
import { User } from "@/types";

export const STAKING_BOOKKEEPER_CONTRACT_SEPOLIA_URL="******************************************"
export const STAKING_BOOKKEEPER_CONTRACT_MAINNET_URL="0x0"

export function getStakingBookkeeperContractURL(networkName: Network = defaultNetwork): Address {
    switch(networkName) {
        case Network.ETH_SEPOLIA:
            return STAKING_BOOKKEEPER_CONTRACT_SEPOLIA_URL;
        case Network.ETH_MAINNET:
            return STAKING_BOOKKEEPER_CONTRACT_MAINNET_URL;
        default:
            throw new Error("Staking Bookkeeper contract not found for network: " + networkName);
    }
}

export function getStakingBookkeeperContract(networkName: Network = defaultNetwork) {
    let contractAddress = getStakingBookkeeperContractURL(networkName);
    return new web3.eth.Contract(contractABI, contractAddress);
}

export async function stakeDetailsFromStakeId(address: Address, stakeId: string, timestamp?: Date) {
    const contract = getStakingBookkeeperContract();
    const stakes = await contract.methods.stakes(address, stakeId).call();
    const stakeDetails = await contract.methods.getStakes(address, stakeId).call();
    const isStakedFromClaim = await contract.methods.isStakedFromClaim(stakeId).call();
    let stakeAtObject = {};
    if (timestamp) {
        const timestampAsUint32 = Math.floor(timestamp.getTime() / 1000);
        timestampAsUint32 >>> 0;
        stakeAtObject = {
            totalStakeAtTimestamp: await contract.methods.getGlobalTotalStakedAt(address, timestampAsUint32).call(),
            globalStakedAtTimestamp: await contract.methods.getGlobalTotalStakedAt(timestampAsUint32).call()
        };
    }
    return {
        stakes,
        stakeDetails,
        isStakedFromClaim,
        ...stakeAtObject,
    };
}

export async function stakingDetails(user: User | undefined | null) {
    if (!user || !user.address) return null;
    const contract = getStakingBookkeeperContract();
    const hasActiveStakes = await contract.methods.activeStakers(user.address).call();
    const activeStakers = await contract.methods.getActiveStakers().call();
    const activeStakesRaw = await contract.methods.getActiveStakesWithId(user.address).call() || []
    const activeStakes = (Object.values(activeStakesRaw)[0] as any[] | undefined )?.map((stakeWithId: any) => {
        return {
            stakeId: stakeWithId.stakeId,
            timestamp: stakeWithId.stake.timestamp,
            timeLock: stakeWithId.stake.timeLock,
            thinkAmount: stakeWithId.stake.thinkAmount,
        };
    }) || [];
    const stakeIds = await contract.methods.stakeIds(user.address).call();
    const totalStakedAmount = await contract.methods.getTotalStakedAmount(user.address).call();
    const totalStaked = await contract.methods.totalStaked(user.address).call();
    const globalStaked = await contract.methods.getGlobalTotalStaked().call();

    return {
        hasActiveStakes, 
        activeStakes,
        activeStakers,
        stakeIds,
        totalStakedAmount,
        totalStaked,
        globalStaked,
    };
}
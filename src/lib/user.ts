import { USER_WALLET_KEY } from "@/components/auth/AuthContext";

/**
 * Get the current user's wallet address from localStorage
 * @returns The wallet address or null if not found
 */
export function getCurrentUserWallet(): string | null {
  if (typeof window === 'undefined') return null;

  return localStorage.getItem(USER_WALLET_KEY);
}

/**
 * Attaches the user's wallet address to a message payload
 * @param messagePayload The original message payload
 * @returns The message payload with the user wallet address attached
 */
export function attachUserToMessage<T extends object>(messagePayload: T): T & { userWalletAddress?: string } {
  const walletAddress = getCurrentUserWallet();

  if (!walletAddress) {
    return messagePayload;
  }

  return {
    ...messagePayload,
    userWalletAddress: walletAddress,
  };
}

/**
 * Example usage for sending a message with user info
 * @param message The message content
 */
export async function sendMessageWithUserInfo(message: string, endpoint: string = '/api/messages'): Promise<Response> {
  const payload = {
    content: message,
    timestamp: new Date().toISOString(),
  };

  // Attach user wallet address to the payload
  const payloadWithUser = attachUserToMessage(payload);

  // Send the message with user info
  return fetch(endpoint, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(payloadWithUser),
  });
}
import { Address } from "viem";
import { defaultNetwork, web3 } from "../web3Config";
import { contractABI } from "./abi-claim"
import { Network } from "alchemy-sdk";

export const CLAIM_CONTRACT_SEPOLIA_URL="******************************************"
export const CLAIM_CONTRACT_MAINNET_URL="0x0"

export function getClaimContractURL(networkName: Network = defaultNetwork): Address {
    switch(networkName) {
        case Network.ETH_SEPOLIA:
            return CLAIM_CONTRACT_SEPOLIA_URL;
        case Network.ETH_MAINNET:
            return CLAIM_CONTRACT_MAINNET_URL;
        default:
            throw new Error("Claim contract not found for network: " + networkName);
    }
}

export function getClaimContract(networkName: Network = defaultNetwork) {
    let contractAddress = getClaimContractURL(networkName);
    return new web3.eth.Contract(contractABI, contractAddress);
}

export async function hasClaimed(networkName: Network, claimId: string) {
    const claimContract = getClaimContract(networkName);
    return claimContract.methods.hasClaimed(claimId).call();
}

export async function isPaused(): Promise<boolean> {
    const claimContract = getClaimContract();
    return await claimContract.methods.paused().call();
}

export function submitClaim(payload: string, signature: string) {
    const claimContract = getClaimContract();
    const data = claimContract.methods.claim(payload, signature).encodeABI();
    const transactionParameters = {
        target: getClaimContractURL(),
        data: data,
    } as {
        target: Address;
        data: `0x${string}`;
    };
    return transactionParameters;
}

// @ts-ignore
export function submitClaimToStake(payload: string, signature: string) {
    const claimContract = getClaimContract();
    // TODO implement claimToStake function that has the same method parameters
    const data = claimContract.methods.claimToStake(payload, signature).encodeABI();
    const transactionParameters = {
        target: getClaimContractURL(),
        data: data,
    } as {
        target: `0x${string}`;
        data: `0x${string}`;
    };
    return transactionParameters;
}
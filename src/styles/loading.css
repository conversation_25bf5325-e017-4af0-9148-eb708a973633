@keyframes dots-1 {
  from {
    opacity: 0;
  }

  25% {
    opacity: 1;
  }
}

@keyframes dots-2 {
  from {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

@keyframes dots-3 {
  from {
    opacity: 0;
  }

  75% {
    opacity: 1;
  }
}

@-webkit-keyframes dots-1 {
  from {
    opacity: 0;
  }

  25% {
    opacity: 1;
  }
}

@-webkit-keyframes dots-2 {
  from {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }
}

@-webkit-keyframes dots-3 {
  from {
    opacity: 0;
  }

  75% {
    opacity: 1;
  }
}

.loading {
  @apply font-display uppercase text-4xl text-red absolute left-1/2 top-1/2 translate-x-[-50%] translate-y-[-50%];
}

.loading .progress {
  @apply bg-red h-0.5 w-0;
}

.loading span {
  animation: dots-1 5s infinite steps(1);
  -webkit-animation: dots-1 5s infinite steps(1);
}

.loading span:first-child+span {
  animation-name: dots-2;
  -webkit-animation-name: dots-2;
}

.loading span:first-child+span+span {
  animation-name: dots-3;
  -webkit-animation-name: dots-3;
}

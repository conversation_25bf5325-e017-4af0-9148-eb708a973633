.Think-V2-Gradient {
  background: linear-gradient(90deg, #0CC1DA 0%, #F4E6BA 50%, #986513 73.5%, #DEAB46 100%);
}

.linear-gradient-teal-to-sand {
  background: linear-gradient(90deg, #0CC1DA 0%, #F4E6BA 50%, #986513 73.5%, #DEAB46 100%);
}

.linear-gradient-sand-to-teal {
  background: linear-gradient(270deg, #0CC1DA 0%, #F4E6BA 50%, #986513 73.5%, #DEAB46 100%);
}

.vertical-gradient-teal-to-sand {
  background: linear-gradient(180deg, #0CC1DA 0%, #F4E6BA 50%, #986513 73.5%, #DEAB46 100%);
}

.vertical-gradient-sand-to-teal {
  background: linear-gradient(0deg, #0CC1DA 0%, #F4E6BA 50%, #986513 73.5%, #DEAB46 100%);
}

.border-gradient-think {
  position: relative;
}

.border-gradient-think::before {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 32px;
  padding: 1px;
  z-index: -1;
  /* background: linear-gradient(180deg, #0CC1DA 0%, #F4E6BA 50%, #986513 73.5%, #DEAB46 100%); */
  background: var(--think-v-2, linear-gradient(0deg, #EDDCAA 0%, #E9DBAB 14%, #DDDBB0 28%, #C9D9B8 41%, #ADD8C3 55%, #89D6D2 69%, #5CD3E4 83%, #29D0F8 96%, #1AD0FF 100%));
  mask:
    linear-gradient(#000 0 0) content-box,
    linear-gradient(#000 0 0);
  mask-composite: exclude;
}

.text-off-white {
  color: #FEFCF4;
}
.bg-off-white {
  background-color: #FEFCF4;
}

/* Accordion */
.st-accordion {
  position: relative;
  overflow: hidden;
}

 .st-accordion__icon::before {
    content: none !important;
  }

  .st-accordion__icon svg {
    transition: transform 0.3s;
    transform: rotate(-90deg);
  }

  .st-accordion__icon.st-accordion__icon--opened svg {
    transform: rotate(0deg);
  }

  .st-accordion p {
    margin-bottom: 1.5rem;
  }

  .st-accordion ol, .st-accordion ul {
    margin-left: 2rem;
  }

  .st-accordion ol {
    list-style-type: decimal;
  }

  .st-accordion ul li,
  .st-accordion ol li {
    margin-bottom: 0.5rem;
  }

  .st-accordion ul {
    list-style-type: disc;
  }

  .st-accordion ol {
    list-style-type: decimal;
  }


.st-accordion__icon--opened {
  div {
    @apply font-token bg-gradient-to-b from-teal to-sand text-transparent bg-clip-text;
  }
}

.st-accordion__content {
  position: relative;
  visibility: visible !important;
  height: 100% !important;
  opacity: 1 !important;
}

.st-accordion__content.st-accordion__content--visible > div {
  margin-top: 0%;
  transition: margin-top 0.2s;
}

.st-accordion .st-accordion__content:not(.st-accordion__content--visible) > div {
  margin-top: -100%;
  transition: margin-top 1.5s;
}


.dashboard-sidebar {
  transition: width 0.4s;
}

.dashboard-sidebar-toggle svg {
  transition: transform 0.4s;
}

.animate-fade-in-out {
  animation: 1s infinite alternate fade-in;
  --animate-fade-in: fade-in 1s linear;
  --animate-fade-out: fade-out 1s linear;

  @keyframes fade-in {
    0% {
      opacity: .2;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes fade-out {
    0% {
      opacity: 1;
    }
    100% {
      opacity: .2;
    }
  }
}

.animate-circular {
  -webkit-animation: rotating 15s linear infinite;
  -moz-animation: rotating 15s linear infinite;
  -ms-animation: rotating 15s linear infinite;
  -o-animation: rotating 15s linear infinite;
  animation: rotating 15s linear infinite;

  @keyframes rotating {
    from {
      -ms-transform: rotate(0deg);
      -moz-transform: rotate(0deg);
      -webkit-transform: rotate(0deg);
      -o-transform: rotate(0deg);
      transform: rotate(0deg);
    }
    to {
      -ms-transform: rotate(360deg);
      -moz-transform: rotate(360deg);
      -webkit-transform: rotate(360deg);
      -o-transform: rotate(360deg);
      transform: rotate(360deg);
    }
  }
}

.dropdown-active {
  .dropdown-item:not(.dropdown-item-active) {
    @apply text-gray-500;

    .text-white {
      @apply text-gray-500;
    }
  }
}

/* Mobile text size overrides for specific pages */
@media (max-width: 768px) {
  /* Override text-6xl to be 3rem instead of 3.75rem on mobile */
  .text-6xl {
    font-size: 3rem !important;
  }
  
  /* Override text-2xl to be 1.45rem with line-height 1.5rem on mobile (card titles) */
  .text-2xl {
    font-size: 1.45rem !important;
    line-height: 1.5rem !important;
  }
  
  /* Override text-4xl to be 1.75rem with line-height 2rem on mobile */
  .text-4xl {
    font-size: 1.75rem !important;
    line-height: 2rem !important;
  }
  
  /* Override heading-display to be 2.75rem on mobile */
  .heading-display {
    font-size: 2.75rem !important;
  }

  /* Make .font-mono.text-gray-200.text-lg slightly smaller on mobile (card body) */
  .font-mono.text-gray-200.text-lg {
    font-size: 1rem !important;
    line-height: 1.55rem !important;
  }
}
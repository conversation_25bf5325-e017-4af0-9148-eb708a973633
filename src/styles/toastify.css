.toastify {
  z-index: 2147483647;
  max-width: calc(50% - 20px);
  transition: all 0.4s cubic-bezier(0.215, 0.61, 0.355, 1);

  @apply px-4 py-3 flex flex-row items-center fixed bg-black border font-mono text-sm tracking-tight border-black-700 text-offWhite no-underline rounded-md cursor-pointer opacity-0;
}

.toastify.on {
  @apply opacity-100;
}

.toast-close {
  @apply text-xl p-0 pl-4 bg-transparent border-0 text-offWhite cursor-pointer opacity-40;
}

.toastify-right {
  @apply right-8;
}

.toastify-left {
  @apply left-8;
}

.toastify-top {
  @apply -top-48;
}

.toastify-bottom {
  @apply -bottom-48;
}

.toastify-rounded {
  @apply rounded-xl;
}

.toastify-avatar {
  @apply h-5 w-5 mr-2;
}

.toastify-center {
  @apply left-0 right-0 mx-auto my-auto max-w-fit;
}

@media only screen and (max-width: 360px) {
  .toastify-right,
  .toastify-left {
    @apply left-0 right-0 mx-auto my-auto max-w-fit;
  }
}

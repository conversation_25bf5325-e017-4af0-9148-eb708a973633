import { useQuery } from "@tanstack/react-query";
import { useAuth } from "../auth/AuthContext";
import { getAstoDetails } from "@/lib/swap/asto";
import { UseQueryResult } from "@tanstack/react-query";
import { createContext, useContext } from "react";
import Big from "big.js";

export interface AstoTokenDetails {
    tokenName: string;
    balance: Big;
    decimals: string;
    symbol: string;
    allowanceForSwap: string;
    totalSupply: string;
}

export interface AstoTokenContextType {
    astoTokenDetailsQuery: UseQueryResult<any | undefined | null> | undefined;
    astoDetails: AstoTokenDetails | undefined | null;
}

export const AstoTokenContext = createContext({
    astoTokenDetailsQuery: undefined,
    astoDetails: undefined,
} as AstoTokenContextType);

export const useAstoToken = () => {
    return useContext(AstoTokenContext);
};

export const AstoTokenProvider = ({ ...props }: React.HTMLAttributes<HTMLDivElement>) => {
    const { user } = useAuth();
    
    if (!user || !user.address) {
        return null;
    }

    const astoTokenDetailsQuery = useQuery({
        queryKey: ["astoTokenDetails", user.address],
        queryFn: async () => {
        if (!user) return null;
        return getAstoDetails(user);
        },
        enabled: !!user,
        refetchOnWindowFocus: "always",
    });

    const context = {
        astoTokenDetailsQuery,
        astoDetails: astoTokenDetailsQuery.data,
    };

    return (
        <AstoTokenContext.Provider value={context}>
            {props.children}
        </AstoTokenContext.Provider>
    );
};

import { useQuery } from "@tanstack/react-query";
import { useAuth } from "../auth/AuthContext";
import { getAstoDetails } from "@/lib/swap/asto";
import { UseQueryResult } from "@tanstack/react-query";
import { createContext, useContext } from "react";
import { getRewardsDetails, getTotalRewardsFromStakers } from "@/lib/rewards-bookkeeper/rewards-bookkeeper";
import { useStake } from "./StakeProvider";

export interface RewardsDetails {
    userTotalRewards: bigint;
    rewardsCount: string;
    globalRewards: bigint;
}

export interface RewardsContextType {
    rewardsDetailsQuery: UseQueryResult<any | undefined | null> | undefined;
    rewardsDetails: RewardsDetails | undefined | null;
}

export const RewardsContext = createContext({
    rewardsDetailsQuery: undefined,
    rewardsDetails: undefined
} as RewardsContextType);

export const useRewards = () => {
    return useContext(RewardsContext);
};

export const RewardsProvider = ({ ...props }: React.HTMLAttributes<HTMLDivElement>) => {
    const { user } = useAuth();
    const { stakeBookkeeperDetails } = useStake();
    
    if (!user || !user.address) {
        return null;
    }

    const rewardsDetailsQuery = useQuery({
        queryKey: ["rewardsDetails", user.address],
        queryFn: async () => {
            if (!user) return null;
            const rewardsDetails = await getRewardsDetails(user);
            if (!rewardsDetails) return null;
            if (stakeBookkeeperDetails?.activeStakers.length === 0) return {
                ...rewardsDetails,
                globalRewards: BigInt(0),
            };
            return {
                ...rewardsDetails,
                globalRewards: await getTotalRewardsFromStakers(stakeBookkeeperDetails?.activeStakers || []),
            }
        },
        enabled: !!user,
        refetchOnWindowFocus: "always",
    });

    const context = {
        rewardsDetailsQuery,
        rewardsDetails: rewardsDetailsQuery.data,
    };

    return (
        <RewardsContext.Provider value={context}>
            {props.children}
        </RewardsContext.Provider>
    );
};

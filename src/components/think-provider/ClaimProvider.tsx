import { useQuery } from "@tanstack/react-query";
import { useAuth } from "../auth/AuthContext";
import { isActiveClaimsTransaction, isClaimsTransaction, isEligibleSnapshotsResponse, web3Client } from "@/lib/api";
import { alchemy } from "@/lib/web3Config";
import { useEffect, useState } from "react";
import { ClaimStakeStages } from "./ThinkDialog";
import { useSendUserOperation, useSmartAccountClient } from "@account-kit/react";
import { ClaimsTransaction, EligibleSnapshotsResponse } from "@/types";
import { UseQueryResult } from "@tanstack/react-query";
import { createContext, useContext } from "react";
import { hasClaimed, isPaused, submitClaim, submitClaimToStake } from "@/lib/claim/claim";
import { calculateAmountFromDecimal } from "../ThinkTokenCard/TokenFunctions";
import { useThinkToken } from "./ThinkTokenProvider";

export interface ClaimableDetails {
    modalStage: ClaimStakeStages;
    isProcessing: boolean;
    totalThinkToClaim: string;
    errorMessage: string | null;
    txHash: string | null;
    totalClaimedTokens: string;
    onClose: () => void;
    reset: () => void;
    submitClaim: () => void;
    submitClaimAndStake: () => void;
}

export interface ClaimContextType {
    claimableQuery: UseQueryResult<EligibleSnapshotsResponse | undefined | null> | undefined;
    claimableResponse?: EligibleSnapshotsResponse;

    totalClaimableTokens?: number;
    totalClaimedTokens?: number;

    claimDetails: ClaimableDetails;
    claimHasGoldenTicket: boolean;
    goldenTicketAmount: number;
}

export const ClaimContext = createContext({
    claimableQuery: undefined,
    claimableResponse: undefined,

    claimHasGoldenTicket: false,
    goldenTicketAmount: 0,
    claimDetails: {
        modalStage: 'initial',
        isProcessing: false,
        totalClaimedTokens: '0',
        totalThinkToClaim: '0',
        errorMessage: null,
        txHash: null,
        onClose: () => { },
        reset: () => { },
        submitClaim: () => { },
        submitClaimAndStake: () => { },
    },

} as ClaimContextType);

export const useClaim = () => {
    return useContext(ClaimContext);
};

export const ClaimProvider = ({ ...props }: React.HTMLAttributes<HTMLDivElement>) => {
    const { user } = useAuth();
    const { thinkTokenDetailsQuery } = useThinkToken();

    // state management for claim and stake
    const [claimStage, setClaimStage] = useState<ClaimStakeStages>('initial');
    const [claimProcessing, setClaimProcessing] = useState<boolean>(false);
    const [claimId, setClaimId] = useState<string | undefined>();
    const [claimTxHash, setClaimTxHash] = useState<string | null>(null);
    const [claimErrorMessage, setClaimErrorMessage] = useState<string | null>(null);
    const [goldenTicketClaimAmount, setGoldenTicketClaimAmount] = useState<number>(0);
    const [pollActiveClaim, setPollActiveClaim] = useState<boolean>(false);
    const [previousActiveClaim, setPreviousActiveClaim] = useState<ClaimsTransaction | undefined>(undefined);

    if (!user || !user.address) {
        return null;
    }

    const isPausedQuery = useQuery({
        queryKey: ["claimPausedQuery"],
        queryFn: async () => {
            const claimHasPaused = await isPaused();
            return claimHasPaused;
        },
        enabled: !!user,
        refetchOnWindowFocus: "always",
    });

    useEffect(() => {
        if (isPausedQuery.data) {
            setClaimStage('paused');
        }
    }, [isPausedQuery.data]);

    const claimableQuery = useQuery({
        queryKey: ["claimablesQuery", user.address],
        queryFn: async () => {
            const response = await web3Client.getWalletEligibility(user.address);
            if (isEligibleSnapshotsResponse(response)) {
                // Lazy calculating golden ticket in all claimables. This will trigger displaying golden ticket
                // Current API logic (June 6, 2025) has golden ticket only included if its counterpart is claimed.
                const totalGoldenTicketAmount = response.claimables.filter(c => c.isGoldenTicket).reduce((acc, c) => acc + parseFloat(calculateAmountFromDecimal(BigInt(c.tokenAmount), BigInt(c.tokenDecimals))), 0);
                setGoldenTicketClaimAmount(totalGoldenTicketAmount);
                return response;
            }
            return undefined;
        },
        enabled: !!user,
        refetchOnWindowFocus: "always",
    });

    useQuery({
        queryKey: ["activeClaimQuery", user.address],
        queryFn: async () => {
            const response = await web3Client.getActiveClaims(user.address);
            if (isActiveClaimsTransaction(response)) {
                if (claimId) {
                    const currentClaim = response.activeClaims.find(c => c.details?.claimId === claimId);
                    switch (currentClaim?.status) {
                        case "completed":
                            setClaimStage('success');
                            setClaimProcessing(false);
                            setPollActiveClaim(false);
                            claimableQuery.refetch();
                            thinkTokenDetailsQuery?.refetch();
                            break;
                        case "failed":
                            setClaimStage('error');
                            setClaimProcessing(false);
                            setPollActiveClaim(false);
                            setClaimErrorMessage("Claim failed. Review Transaction on Explorer.");
                            break;
                        default:
                            break;
                    }
                } else {
                    const lastPendingProcessClaim = response.activeClaims.find(c => c.status === "pending" || c.status === "processing");
                    setPreviousActiveClaim(lastPendingProcessClaim);
                    if (lastPendingProcessClaim && lastPendingProcessClaim.status === "processing") {
                        const pollTransaction = lastPendingProcessClaim.details?.pollTransactions[0] || null;
                        if (pollTransaction) {
                            // this could either be claiming or claiming staking, but won't know - default to claiming
                            setClaimStage('claiming');
                            setClaimId(lastPendingProcessClaim.details?.claimId);
                            setClaimProcessing(true);
                            setPollActiveClaim(pollTransaction.activePolling);
                            setClaimTxHash(pollTransaction.transactionHash);
                        }
                    }
                }
                return response;
            }
            return undefined;
        },
        enabled: true,
        refetchOnWindowFocus: "always",
        refetchInterval: pollActiveClaim ? 5000 : false,
    });

    const {
        sendUserOperation: claimOperation,
        isSendingUserOperation: isSendingClaimOperation
    } = useSendUserOperation({
        client: undefined,
        waitForTxn: true,
        onSuccess: ({ hash }: { hash: string }) => {
            web3Client.createPollTransaction(user.address, claimId!, hash);
            setPollActiveClaim(true);
            setClaimTxHash(hash);
        },
        onError: (error: any) => {
            setClaimErrorMessage(error.message || null);
            setClaimStage('error');
            setClaimProcessing(false);
        },
    });

    useEffect(() => {
        if (isSendingClaimOperation) {
            setClaimProcessing(true);
        }
    }, [isSendingClaimOperation]);

    const totalClaimedTokens = claimableQuery.data?.claimables
        .filter((c) => c.claimStatus === "claimed")
        .reduce((acc, c) => acc + parseFloat(calculateAmountFromDecimal(BigInt(c.tokenAmount), BigInt(c.tokenDecimals))), 0) || 0;
    const totalClaimableTokens = claimableQuery.data?.claimables
        .filter((c) => c.claimStatus !== "claimed")
        .filter((c) => !c.isGoldenTicket)
        .reduce((acc, c) => acc + parseFloat(calculateAmountFromDecimal(BigInt(c.tokenAmount), BigInt(c.tokenDecimals))), 0) || 0;
    const claimHasGoldenTicket = goldenTicketClaimAmount && parseFloat(goldenTicketClaimAmount.toString()) > 0 ? true : false;
    const claimableIds = claimableQuery.data?.claimables.map((c) => c.id as number) || [];

    const getPayloadAndSubmit = async (actionStage: ClaimStakeStages, submitAction: (payload: string, signature: string) => void) => {
        setClaimStage('preparing');
        setClaimProcessing(true);
        try {
            let activeClaim = previousActiveClaim;
            if (!activeClaim) {
                const claimPayloadResponse = await web3Client.createClaimsTransaction(user.address, claimableIds);
                activeClaim = isClaimsTransaction(claimPayloadResponse) ? claimPayloadResponse : undefined;
                setPreviousActiveClaim(activeClaim);
            }
            if (activeClaim && activeClaim.claimPayload) {
                const network = activeClaim.details?.networkName;
                setClaimId(activeClaim.details?.claimId);
                const pollTransaction = activeClaim.details?.pollTransactions[0] || null;
                switch (activeClaim.status) {
                    // @ts-ignore
                    case "pending":
                        setClaimStage(actionStage);
                        submitAction(activeClaim.claimPayload.payload, activeClaim.claimPayload.signature);
                        if (pollTransaction) {
                            web3Client.createPollTransaction(user.address, claimId!, pollTransaction.transactionHash);
                        }
                    case "processing":
                        if (pollTransaction) {
                            setClaimStage(actionStage);
                            setPollActiveClaim(pollTransaction.activePolling);
                            setClaimTxHash(pollTransaction.transactionHash);
                        }
                        break;
                    default:
                        if (network && claimId && await hasClaimed(network, claimId)) {
                            setClaimStage('error');
                            setClaimProcessing(false);
                            setClaimErrorMessage("Already claimed");
                            return;
                        }
                        break;
                }
            } else {
                setClaimStage('error');
                setClaimProcessing(false);
                setClaimErrorMessage("Unknown error. Try again later.");
                console.error("Unexpected response creating claim", activeClaim)
            }
        } catch (err: any) {
            console.error(err);
            setClaimStage('error');
            setClaimProcessing(false);
            setClaimErrorMessage(err.message || null);
        }
    }

    const context = {
        totalClaimableTokens: totalClaimableTokens,
        totalClaimedTokens: totalClaimedTokens,
        claimableQuery,
        claimableResponse: claimableQuery.data,

        goldenTicketAmount: goldenTicketClaimAmount,
        claimHasGoldenTicket,

        claimDetails: {
            modalStage: claimStage,
            totalClaimedTokens: totalClaimedTokens.toFixed(2),
            totalThinkToClaim: totalClaimableTokens.toFixed(2),
            isProcessing: claimProcessing,
            errorMessage: claimErrorMessage,
            txHash: claimTxHash,
            onClose: () => { },
            reset: () => {
                setClaimStage('initial');
                setClaimProcessing(false);
                setClaimTxHash(null);
            },
            submitClaim: async () => {
                await getPayloadAndSubmit("claiming", (payload, signature) => {
                    const transactionParams = submitClaim(payload, signature);
                    claimOperation({ uo: transactionParams });
                });
            },
            submitClaimAndStake: async () => {
                await getPayloadAndSubmit("claiming_staking", (payload, signature) => {
                    const transactionParams = submitClaimToStake(payload, signature);
                    claimOperation({ uo: transactionParams });
                });
            },
        },
    };

    return (
        <ClaimContext.Provider value={context}>
            {props.children}
        </ClaimContext.Provider>
    );
};

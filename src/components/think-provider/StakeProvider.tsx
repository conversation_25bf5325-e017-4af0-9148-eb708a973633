import { useAuth } from "../auth/AuthContext";
import { useEffect, useState } from "react";
import { useSendUserOperation, useSmartAccountClient } from "@account-kit/react";
import { createContext, useContext } from "react";
import { ThinkTokenProvider, TokenDetails, useThinkToken } from "./ThinkTokenProvider";
import { StakeStages } from "./ThinkDialog";
import Big from "big.js";
import { addStakeAllowanceTransaction } from "@/lib/swap/think";
import { calculateAmountFromDecimal } from "../ThinkTokenCard/TokenFunctions";
import { set } from "date-fns";
import { useQuery, UseQueryResult } from "@tanstack/react-query";
import { stakingDetails } from "@/lib/staking-bookkeeper/staking-bookkeeper";
import { isPaused, stake, unstake } from "@/lib/staking-vault/staking-vault";
import { Address } from "viem";

export interface StakeDetails {
    modalStage: StakeStages;
    isProcessing: boolean;
    errorMessage: string | null;
    totalThinkStaked: string;
    totalThinkToStake: string;
    totalStakeAllowance: string;
    meetsMinimumToStake: boolean;
    txHash: string | null;
    onClose: () => void;
    reset: () => void;
    approveAllowance: () => void;
    submitStake: () => void;
    submitUnstake: (stakeId: string) => void;
}

export interface ActiveStake {
    stakeId: string;
    timestamp: number;
    timeLock: number;
    thinkAmount: string;
}

export interface StakeBookkeeperDetails {
    hasActiveStakes: boolean;
    activeStakes: ActiveStake[];
    activeStakers: Address[];
    stakeIds: any;
    totalStakedAmount: string;
    totalStaked: string;
    globalStaked: string;
}

export interface StakeContextType {
    changeStakeAmount: (amount: number) => void;
    stakeDetails: StakeDetails;
    stakeBookkeeperDetailsQuery: UseQueryResult<StakeBookkeeperDetails | undefined | null> | undefined;
    stakeBookkeeperDetails: StakeBookkeeperDetails | null | undefined;
}

export const StakeContext = createContext({
    changeStakeAmount: (_: number) => { },
    stakeDetails: {
        modalStage: 'initial',
        isProcessing: false,
        errorMessage: null,
        totalThinkStaked: '0',
        totalThinkToStake: '0',
        meetsMinimumToStake: false,
        totalStakeAllowance: '0',
        txHash: null,
        onClose: () => { },
        reset: () => { },
        approveAllowance: () => { },
        submitStake: () => { },
        submitUnstake: (_: string) => { },
    },
    stakeBookkeeperDetailsQuery: undefined,
    stakeBookkeeperDetails: undefined,
} as StakeContextType);

export const useStake = () => {
    return useContext(StakeContext);
};

export const MINIMUM_STAKE_AMOUNT = 0.**********;

export const StakeProvider = ({ ...props }: React.HTMLAttributes<HTMLDivElement>) => {
    const { user } = useAuth();

    // require asto provider and details. return null if not available.
    const { thinkTokenDetails, thinkTokenDetailsQuery } = useThinkToken();

    const isPausedQuery = useQuery({
        queryKey: ["stakePausedQuery"],
        queryFn: async () => {
            const claimHasPaused = await isPaused();
            return claimHasPaused;
        },
        enabled: !!user,
        refetchOnWindowFocus: "always",
    });

    useEffect(() => {
        if (isPausedQuery.data) {
            setStakeStage('paused');
        } 
    }, [isPausedQuery.data]);

    const checkInitialStage = (details?: TokenDetails | null) => {
        let initialStage: StakeStages = "initial";
        if (!details) return initialStage;
        const totalAllowance = calculateAmountFromDecimal(BigInt(details.stakeAllowance), BigInt(details.decimals));
        if (isPausedQuery.data) {
            initialStage = "paused";
        } else if (new Big(totalAllowance).gte(details.balance) && details.balance.gt(new Big(MINIMUM_STAKE_AMOUNT))) {
            initialStage = "approved";
        }
        return initialStage;
    }

    // state management for stake
    const [stakeStage, setStakeStage] = useState<StakeStages>(checkInitialStage(thinkTokenDetails));
    const [stakeProcessing, setStakeProcessing] = useState<boolean>(false);
    const [stakeTxHash, setStakeTxHash] = useState<string | null>(null);
    const [unstakeIds, setUnstakeIds] = useState<string[]>([]);
    const [stakeErrorMessage, setStakeErrorMessage] = useState<string | null>(null);
    const [totalThinkToStake, setTotalThinkToStake] = useState<string>("");
    const [intervalCheckAllowanceId, setIntervalCheckAllowanceId] = useState<NodeJS.Timeout | undefined>();
    const [attemptsToConfirmAllowance, setAttemptsToConfirmAllowance] = useState<number>(0);
    const [intervalCheckStakingId, setIntervalCheckStakingId] = useState<NodeJS.Timeout | undefined>();
    const [attemptsToConfirmStaking, setAttemptsToConfirmStaking] = useState<number>(0);
    const [totalThinkStaked, setTotalThinkStaked] = useState<number>(0);

    const stakeBookkeeperDetailsQuery = useQuery({
        queryKey: ["stakeBookkeeperDetails", user && user.address],
        queryFn: async () => {
            if (!user) return null;
            const results = await stakingDetails(user);
            return results;
        },
        enabled: !!user,
        refetchOnWindowFocus: "always",
    });

    // update the balance to stake if the balance of think has changed.
    useEffect(() => {
        if (thinkTokenDetails?.balance && totalThinkToStake === "") {
            const totalThinkInBase = new Big(thinkTokenDetails.balance).mul(new Big(10).pow(parseInt(thinkTokenDetails.decimals))).toFixed(0);
            setTotalThinkToStake(totalThinkInBase);
        }
    }, [thinkTokenDetails?.balance]);

    // if the token allowance has changed, check the approval process.
    useEffect(() => {
        if (stakeStage === "approving" && checkInitialStage(thinkTokenDetails) === "approved") {
            setStakeStage("approved");
            setStakeProcessing(false);
            if (intervalCheckAllowanceId !== undefined) clearInterval(intervalCheckAllowanceId)
            setAttemptsToConfirmAllowance(0);
            setIntervalCheckAllowanceId(undefined);
        } else if (attemptsToConfirmAllowance >= 60) {
            setStakeStage("error");
            setStakeErrorMessage("Taking longer than expected to confirm allowance. Try refreshing the page or come back later. Contact support if the issue persists.");
            setStakeProcessing(false);
            setAttemptsToConfirmAllowance(0);
            if (intervalCheckAllowanceId !== undefined) clearInterval(intervalCheckAllowanceId)
            setIntervalCheckAllowanceId(undefined);
        }

        if (stakeStage === "initial") {
            const initialStage = checkInitialStage(thinkTokenDetails);
            if (initialStage === "approved") {
                setStakeStage("approved");
            }
        }
    }, [thinkTokenDetails?.stakeAllowance]);

    // if the active staking has changed and recently has new staking
    useEffect(() => {
        const activeStakes = stakeBookkeeperDetailsQuery?.data?.activeStakes || [];
        let mostRecentStake: any | null = null;
        activeStakes.forEach((stake: any) => {
            if (!mostRecentStake) {
                mostRecentStake = stake;
            } else {
                if (stake.timestamp > mostRecentStake.timestamp) {
                    mostRecentStake = stake;
                }
            }
        });

        const stakedIds = stakeBookkeeperDetailsQuery?.data?.stakeIds || undefined;
        const unstakeIdsNotInStakeIds = Array.isArray(stakedIds) && unstakeIds.every(id => !stakedIds.includes(id))

        // checked to see if staked in the last 10 minutes
        const thirtyMinutesAgo = new Date(Date.now() - 10 * 60 *1000).getTime();
        const mostRecentStakeTimestamp = mostRecentStake ? new Date(mostRecentStake.timestamp * 1000).getTime() : 0;
        
        if (attemptsToConfirmStaking >= 60) {
            setStakeStage("error");
            setStakeErrorMessage("Taking longer than expected to confirm staking. Try refreshing the page or come back later. Contact support if the issue persists.");
            setStakeProcessing(false);
            setAttemptsToConfirmStaking(0);
            if (intervalCheckStakingId !== undefined) clearInterval(intervalCheckStakingId)
            setIntervalCheckStakingId(undefined);
        } else if (stakeStage === "unstaking" && unstakeIdsNotInStakeIds) {
            setStakeStage("unstaked");
            setStakeProcessing(false);
            if (intervalCheckStakingId !== undefined) clearInterval(intervalCheckStakingId)
            setAttemptsToConfirmStaking(0);
            setIntervalCheckStakingId(undefined);
            setUnstakeIds([]);
        } else if (stakeStage === "staking" && mostRecentStakeTimestamp > thirtyMinutesAgo) {
            setStakeStage("success");
            setStakeProcessing(false);
            setTotalThinkStaked(parseFloat(mostRecentStake.thinkAmount));
            if (intervalCheckStakingId !== undefined) clearInterval(intervalCheckStakingId)
            setAttemptsToConfirmStaking(0);
            setIntervalCheckStakingId(undefined);
        }
    }, [stakeBookkeeperDetailsQuery?.data]);

    // if the staking stage has changed, check to see if the allowance is updated.
    useEffect(() => {
        if (stakeStage === "approving") {
            const intervalId = setInterval(() => {
                thinkTokenDetailsQuery?.refetch();
                setAttemptsToConfirmAllowance(attemptsToConfirmAllowance + 1);
            }, 5000);
            setIntervalCheckAllowanceId(intervalId);
        } else if (stakeStage === "staking" || stakeStage === "unstaking") {
            const intervalId = setInterval(() => {
                stakeBookkeeperDetailsQuery?.refetch();
                setAttemptsToConfirmStaking(attemptsToConfirmStaking + 1);
            }, 5000);
            setIntervalCheckStakingId(intervalId);
        }
    }, [stakeStage]);

    const { 
        sendUserOperation: stakeApproveOperation
    } = useSendUserOperation({
        client: undefined,
        waitForTxn: true,
        onSuccess: ({ hash }: { hash: string }) => {
            setStakeTxHash(hash);
            // force refresh and use effect to check if moved from approving to approved
            thinkTokenDetailsQuery?.refetch();
        },
        onError: (error: any) => {
            setStakeErrorMessage(error.message || null);
            setStakeStage('error');
            setStakeProcessing(false);
        },
    });
    const { 
        sendUserOperation: stakeOperation
    } = useSendUserOperation({
        client: undefined,
        waitForTxn: true,
        onSuccess: ({ hash }: { hash: string }) => {
            setStakeTxHash(hash);
            // force refresh and use effect to check if moved from staking to success
            stakeBookkeeperDetailsQuery?.refetch();
        },
        onError: (error: any) => {
            setStakeErrorMessage(error.message || null);
            setStakeStage('error');
            setStakeProcessing(false);
        },
    });

    if (!thinkTokenDetails) {
        return null;
    }
    if (!user || !user.address) {
        return null;
    }

    let totalStakeAllowance = calculateAmountFromDecimal(BigInt(thinkTokenDetails.stakeAllowance), BigInt(thinkTokenDetails.decimals), 2);
    if (new Big(totalStakeAllowance).gt(thinkTokenDetails.balance)) {
        totalStakeAllowance = thinkTokenDetails.balance.toFixed(2);
    }

    const context = {
        // change stake amount in human readable amounts (i.e. 10, 20)
        // NOT in base units (i.e. 1000000000000000000 for 1 token)
        changeStakeAmount: (amount: number) => {
            const initialStage = checkInitialStage(thinkTokenDetails);
            if (stakeStage === "initial" && initialStage === "approved") {
                setStakeStage("approved");
            }
            const totalThinkInBase = new Big(amount).mul(new Big(10).pow(parseInt(thinkTokenDetails.decimals))).toFixed(0);
            setTotalThinkToStake(totalThinkInBase);
        },
        stakeDetails: {
            modalStage: stakeStage,
            isProcessing: stakeProcessing,
            errorMessage: stakeErrorMessage,
            txHash: stakeTxHash,
            meetsMinimumToStake: thinkTokenDetails.balance.gte(new Big(MINIMUM_STAKE_AMOUNT)),
            totalThinkStaked: calculateAmountFromDecimal(BigInt(totalThinkStaked), BigInt(thinkTokenDetails.decimals), 2),
            totalThinkToStake: thinkTokenDetails.balance.toFixed(2),
            totalStakeAllowance: calculateAmountFromDecimal(BigInt(thinkTokenDetails.stakeAllowance), BigInt(thinkTokenDetails.decimals), 2),
            onClose: () => { },
            reset: () => {
                const initialStage = checkInitialStage(thinkTokenDetails);
                setStakeStage(initialStage);
                setStakeProcessing(false);
                setStakeTxHash(null);
            },
            submitUnstake: (stakeId: string) => { 
                setStakeStage('unstaking'); 
                setUnstakeIds(unstakeIds.concat([stakeId]));
                const unstakeParams = unstake(stakeId);
                stakeOperation({ uo: unstakeParams });
                setStakeProcessing(true);
            },
            approveAllowance: async () => { 
                const approveTx = addStakeAllowanceTransaction(totalThinkToStake);
                stakeApproveOperation({ uo: { ...approveTx } });
                setStakeStage('approving'); 
                setStakeProcessing(true);
            },
            submitStake: () => { 
                setStakeProcessing(true);
                setStakeStage('allowance_check');
                const initialStage = checkInitialStage(thinkTokenDetails);
                if (initialStage === "approved") {
                    // TODO: figure out timelock
                    const transactionParams = stake(totalThinkToStake, "0");
                    setStakeStage('staking');
                    stakeOperation({ uo: transactionParams });
                } else {
                    setStakeStage('error');
                    setStakeErrorMessage("Allowance not confirmed. Please try again.");
                    setStakeProcessing(false);
                }
            },
        },
        stakeBookkeeperDetailsQuery,
        stakeBookkeeperDetails: stakeBookkeeperDetailsQuery.data,
    };

    return (
        <ThinkTokenProvider>
            <StakeContext.Provider value={context}>
                {props.children}
            </StakeContext.Provider>
        </ThinkTokenProvider>
    );
};

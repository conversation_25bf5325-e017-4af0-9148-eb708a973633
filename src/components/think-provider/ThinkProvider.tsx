import { createContext, useContext } from "react";
import { Clai<PERSON>Provider } from "./ClaimProvider";
import { AstoTokenProvider } from "./AstoTokenProvider";
import { StakeProvider } from "./StakeProvider";
import { ThinkTokenProvider } from "./ThinkTokenProvider";
import { useAuth } from "../auth/AuthContext";
import { WalletButton } from "../ui/wallet-button";

export interface ThinkContextType {}

export const ThinkContext = createContext({} as ThinkContextType);

export const useThink = () => {
    return useContext(ThinkContext);
};

export const ThinkProvider = ({ ...props }: React.HTMLAttributes<HTMLDivElement>) => {
    const { user } = useAuth();

    const context = {
    };

    if (!user) {
        return (
            <div >
                <div className="my-8 bg-black text-white shadow-lg rounded-[32px] sm:max-w-[640px] max-w-[90vw] mx-auto">
                    <div className="p-6 pb-12 border-r-2 border-l-2 border-b-2 border-neutral-800 rounded-[32px]">
                        <div >
                            <div className="block w-[40%] m-auto my-12 mb-8 relative">
                                <div style={{boxShadow: "inset 0 0 150px #0E0F12"}} className="absolute w-full h-full"  />
                                <video autoPlay={true} loop muted playsInline={true} className="">
                                    <source src="videos/CoinLoop.webm" type="video/webm" />
                                    <source src="videos/CoinLoop-opt.mp4" type="video/mp4" />
                                </video>
                            </div>
                            <div className="block w-[80%] m-auto my-12 mb-8 relative">
                                <p className="text-white text-2xl mb-2 text-center uppercase font-token">
                                    Add Your Wallet
                                </p>
                                <p className="font-mono text-gray-400 text-center">
                                    Add your wallet to see how much $THINK you can claim. The wallet you connect will be what you will use to stake and withdraw your $THINK.
                                </p>
                            </div>
                        </div>
                        <div className="mt-4">
                            <div className="flex flex-col space-y-6 w-full align-center">
                                <div className="flex flex-row space-x-2 block block px-8">
                                    <WalletButton variant="gradient" size="full" />
                                </div>
                                <p className="font-mono text-sm text-gray-400 text-center block px-12">
                                    By continuing you agree to our <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">terms of service</a> and <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">privacy policy</a>.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <ThinkTokenProvider>
            <StakeProvider>
                <ClaimProvider>
                    <ThinkContext.Provider value={context}>
                        {props.children}
                    </ThinkContext.Provider>
                </ClaimProvider>
            </StakeProvider>
        </ThinkTokenProvider>
    );
};

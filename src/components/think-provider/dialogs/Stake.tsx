import { getFriendlyErrorMessage, formatNumberWithCommas, getExplorerUrl } from "@/components/ThinkTokenCard/TokenFunctions";
import { StakeStages, ThinkDialogButton, ThinkDialogContents } from "../ThinkDialog";
import { MINIMUM_STAKE_AMOUNT, StakeDetails } from "../StakeProvider";

export const StakeDialogContent: (obj: StakeDetails) => ThinkDialogContents = ({
    modalStage,
    isProcessing,
    errorMessage,
    txHash,
    totalThinkToStake,
    totalThinkStaked,
    totalStakeAllowance,
    meetsMinimumToStake,
    onClose,
    approveAllowance,
    submitStake,
    reset,
}) => {

    const TitleComponent = () => (
        <>
            {modalStage === 'unstaking' && "Unstake $THINK"}
            {modalStage !== 'unstaking' && "Stake $THINK"}
        </>
    );

    const DescriptionComponent = () => {
        let description: React.ReactNode;
        switch (true) {
            case modalStage === 'paused':
                description = (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_unsuccessful.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            Staking Disabled
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            Staking is currently disabled. 
                        </p>
                        { errorMessage && (
                            <div className="font-mono text-red uppercase">Reach out to discord if you think this is an error.</div>
                        )}
                    </>
                );
                break;
            case modalStage === 'success':
                description = (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_successful.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            Transaction Successful
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            You now have Staked {formatNumberWithCommas(totalThinkStaked)} $THINK.
                        </p>
                    </>
                );
                break;
            case modalStage === 'approved':
                description = (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_coin_w_rays.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            Stake Think
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            You have approved {formatNumberWithCommas(totalStakeAllowance)} $THINK for staking. You can now proceed to stake your $THINK.
                        </p>
                    </>
                );
                break;
            case modalStage === 'error':
                description = (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_unsuccessful.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            Transaction Unsuccessful
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            An error has occurred during the transaction. Please try again.
                        </p>
                        { errorMessage && (
                            <div className="font-mono text-red uppercase">Error: {getFriendlyErrorMessage(errorMessage)}</div>
                        )}
                    </>
                );
                break;
            case isProcessing:
                description = (
                    <>
                        <div className="block w-[65%] m-auto my-4 mb-8 relative">
                            <img className="block w-full opacity-0" src="/images/logos/knocked_out_coin_w_rays.svg"></img>
                            <img className="block w-full left-0 top-0 animate-circular absolute" src="/images/dialogs/knocked_out_coin_rays-only.png"></img>
                            <img style={{ transform: "translateY(-50%) translateX(-50%)"}} className="w-[45%] block left-[50%] top-[50%] absolute" src="/images/dialogs/knocked-out-coin.svg"></img>
                        </div>
                        <p className="text-white text-2xl mb-2">
                            {modalStage === 'initial' && "You are claiming all $THINK"}
                            {modalStage === 'approving' && "Approving Allowance"}
                            {modalStage as StakeStages === 'approved' && "Allowance Approved"}
                            {modalStage === 'allowance_check' && "Confirming Allowance"}
                            {modalStage === 'staking' && "Staking $THINK"}
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            This Can take a few minutes. Please wait while the transaction completes.
                        </p>
                    </>
                );
                break;
            case modalStage === 'initial' && meetsMinimumToStake:
                description =  (
                    <>
                        <img className="block w-[65%] m-auto py-4 pb-8 " src="/images/logos/knocked_out_coin_w_rays.svg"></img>
                        <p className="text-white text-lg">
                            You are claiming all $THINK
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            You will stake {formatNumberWithCommas(totalThinkToStake)} $THINK.
                        </p>
                    </>
                );
                break;
            case modalStage === 'initial' && !meetsMinimumToStake:
                description =  (
                    <>
                        <img className="block w-[65%] m-auto py-4 pb-8 " src="/images/logos/knocked_out_coin_w_rays.svg"></img>
                        <p className="text-white text-lg">
                            Insufficient $THINK
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            Please purchase more $THINK to stake and return here for earning rewards.
                        </p>
                    </>
                );
                break;
        }

        return (
            <div className="text-center text-lg">
                {description}
            </div>
        )
    };

    const FooterComponent = () => {
        let action: React.ReactNode;
        switch (true) {
            case modalStage === 'paused':
                action = (
                    <ThinkDialogButton onClick={onClose}>
                        Close
                    </ThinkDialogButton>
                );
                break;
            case isProcessing:
                action = (
                    <ThinkDialogButton disabled={true} onClick={() => {}}>
                        in Progress
                    </ThinkDialogButton>
                );
                break;
            case modalStage !== "approved" && !meetsMinimumToStake:
                action = (
                    <ThinkDialogButton onClick={onClose}>
                        Close
                    </ThinkDialogButton>
                );
                break;
            case modalStage === 'initial':
                action = (
                    <>
                        <ThinkDialogButton onClick={approveAllowance}>
                            Approve Allowance
                        </ThinkDialogButton>
                    </>
                );
                break;
            case modalStage === 'approved':
                action = (
                    <>
                        <ThinkDialogButton onClick={submitStake}>
                            Stake $THINK
                        </ThinkDialogButton>
                    </>
                );
                break;
            case modalStage === 'success':
                action = (
                    <ThinkDialogButton onClick={onClose}>
                        Close
                    </ThinkDialogButton>
                );
                break;
            case modalStage === 'error':
                action = (
                    <ThinkDialogButton onClick={reset}>
                        Try Again
                    </ThinkDialogButton>
                );
                break;
            default:
                action = (
                    <ThinkDialogButton disabled={true} onClick={() => { window.location.reload(); }}>
                        Error. Click to Reload
                    </ThinkDialogButton>
                );
                break;
        }

        return (
            <div className="flex flex-col space-y-6 w-full">
                <div className="flex flex-row space-x-2">
                    {action}
                </div>

                {(modalStage === "initial" || !txHash) && (
                    <p className="font-mono text-sm text-gray-400">
                        By continuing you agree to our <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">terms of service</a> and <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">privacy policy</a>.
                    </p>
                )}
                {modalStage !== "initial" && txHash && (
                    <p className="font-mono text-sm text-gray-400 text-center uppercase">
                        View Transaction on <a href={`${getExplorerUrl()}/tx/${txHash}`} target="_blank" rel="noopener noreferrer" className="underline text-[#22D0FB] hover:text-[#22D0FB]">Etherscan</a>
                    </p>
                )}
            </div>
        );
    }


    return {
        TitleComponent,
        DescriptionComponent,
        FooterComponent,
    };
};

import { truncateAddress, getFriendlyErrorMessage, formatNumberWithCommas, getExplorerUrl } from "@/components/ThinkTokenCard/TokenFunctions";
import Big from "big.js";
import { ThinkDialogButton, ThinkDialogContents } from "../ThinkDialog";
import { useTranslation } from "@/hooks/useTranslation";


interface DialogProps {
    goldenTicketDetails: GoldenTicketDetails;
    onClose: () => void;
}

interface GoldenTicketDetails {
    totalGoldenTicketThink: string;
    txHash?: string | null;
}

export const GoldenTicketDialogContent: (obj: DialogProps) => ThinkDialogContents = ({
    onClose,
    goldenTicketDetails
}) => {
    const { t } = useTranslation();

    const TitleComponent = () => null;

    const DescriptionComponent = () => {
        return (
            <div className="text-center text-lg">
                <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_golden_ticket.svg"></img>
                <p className="text-black text-2xl mb-2">
                    {t('dialogs.goldenTicket.title')}
                </p>
                <p className="font-mono text-[#99662F] uppercase">
                    {t('dialogs.goldenTicket.description', { amount: formatNumberWithCommas(goldenTicketDetails.totalGoldenTicketThink) })}
                </p>
            </div>
        )
    };

    const FooterComponent = () => {
        return (
            <div className="flex flex-col space-y-6 w-full">
                <div className="flex flex-row space-x-2">
                    <ThinkDialogButton theme="secondary" onClick={onClose}>
                        {t('common.buttons.close')}
                    </ThinkDialogButton>
                </div>
                {goldenTicketDetails.txHash && (
                    <p className="font-mono text-[#99662F] text-center uppercase">
                        {t('dialogs.goldenTicket.viewTransaction')} <a href={`${getExplorerUrl()}/tx/${goldenTicketDetails.txHash}`} target="_blank" rel="noopener noreferrer" className="underline text-[#000000] hover:text-[#000000]">{t('dialogs.goldenTicket.etherscan')}</a>
                    </p>
                )}
            </div>
        );
    }


    return {
        TitleComponent,
        DescriptionComponent,
        FooterComponent,
    };
};

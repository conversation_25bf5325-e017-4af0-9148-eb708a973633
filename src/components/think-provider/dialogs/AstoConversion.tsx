import { truncateAddress, getFriendlyErrorMessage } from "@/components/ThinkTokenCard/TokenFunctions";
import Big from "big.js";
import { AstoConversionStages, ThinkDialogButton, ThinkDialogContents } from "../ThinkDialog";


interface DialogProps {
    modalStage: AstoConversionStages;
    isProcessing: boolean;
    astoBurnDetails: AstoBurnDetails;
}

interface AstoBurnDetails {
    astoBalanceToBurn: string;
    calculatedThinkToReceive: string;
    approvalTxHash?: string | null;
    burnTxHash?: string | null;
    explorerUrl?: string;
    errorMessage?: string | null;
    onApprove: () => void;
    onConfirmBurn: () => void;
    onCancel: () => void;
}

interface ConversionInputProps {
    tokenName: string;
    tokenBalance: string;
}

const ConversionInput: React.FC<ConversionInputProps> = ({ tokenName, tokenBalance }) => {
    return (
        <div className="text-white text-xl bg-black-800 rounded-xl p-3 mb-4 mt-2 flex flex-row justify-between items-center">
            <span className="font-[evolver-variable]">
                {new Big(tokenBalance || 0).toFixed(4)}
            </span>
            <div className="flex flex-row items-center border border-black-700 rounded-xl p-2">
                <div className="relative mr-2">
                    <img src="/images/logos/eth-icon.png" alt="eth" className="w-4 h-4 ml-2 absolute bottom-0 right-0" />
                    {tokenName === "ASTO" && <img src="/images/logos/asto-icon.png" alt="ASTO" className="w-8 h-8 mr-2" />}
                    {tokenName === "THINK" && <img src="/images/think-icon.svg" alt="THINK" className="w-8 h-8 mr-2 border border-white rounded-full" />}
                </div>
                {tokenName}
            </div>
        </div>
    );
};

export const AstoConverterDialogContent: (obj: DialogProps) => ThinkDialogContents = ({
    modalStage,
    isProcessing,
    astoBurnDetails
}) => {

    const TitleComponent = () => (
        <>
            {modalStage === 'initial' && (<> <img src="/images/logos/asto-icon.png" alt="ASTO Logo" className="w-8 h-8 mr-3" /> Convert $ASTO to $THINK</>)}
            {modalStage === 'approving' && "Approving ASTO..."}
            {modalStage === 'approved' && "ASTO Approved"}
            {modalStage === 'burning' && "Burning ASTO..."}
            {modalStage === 'success' && "Burn Successful!"}
            {modalStage === 'error' && "Transaction Error"}
        </>
    );

    const DescriptionComponent = () => (
        <>
            <div className="font-mono  text-gray-400">
                {modalStage === 'paused' && (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_unsuccessful.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            Swapping Disabled
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            Swapping is currently disabled. Reach out to Discord if you believe this is an error.
                        </p>
                    </>
                )}
                {!isProcessing && modalStage === 'initial' && (
                    <>
                        <p>
                            By continuing you agree to our <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">terms of service</a> and <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">privacy policy</a>.
                        </p>
                        <div className="pt-4">
                            <label htmlFor="asto-amount" className="text-sm text-gray-400">YOU ARE BURNING</label>
                            <ConversionInput tokenName="ASTO" tokenBalance={astoBurnDetails.astoBalanceToBurn} />
                            <label htmlFor="think-amount" className="text-sm text-gray-400">YOU ARE RECEIVING</label>
                            <ConversionInput tokenName="THINK" tokenBalance={astoBurnDetails.calculatedThinkToReceive} />
                            <br />This requires two transactions: one to approve ASTO spending, and one to perform the burn.
                        </div>
                    </>
                )}

                {modalStage === 'approving' && (
                    <div className="flex flex-col items-center justify-center">
                        <div className="block w-[65%] m-auto my-4 mb-8 relative">
                            <img className="block w-full opacity-0" src="/images/logos/knocked_out_coin_w_rays.svg"></img>
                            <img className="block w-full left-0 top-0 animate-circular absolute" src="/images/dialogs/knocked_out_coin_rays-only.png"></img>
                            <img style={{ transform: "translateY(-50%) translateX(-50%)"}} className="w-[45%] block left-[50%] top-[50%] absolute" src="/images/dialogs/knocked-out-coin.svg"></img>
                        </div>
                        <p>Approving ASTO spend...</p>
                        {isProcessing && <div className="text-center my-4">Processing transaction, please wait...</div>}
                    </div>
                )}

                {modalStage === 'burning' && (
                    <div className="flex flex-col items-center justify-center">
                        <div className="block w-[65%] m-auto my-4 mb-8 relative">
                            <img className="block w-full opacity-0" src="/images/logos/knocked_out_coin_w_rays.svg"></img>
                            <img className="block w-full left-0 top-0 animate-circular absolute" src="/images/dialogs/knocked_out_coin_rays-only.png"></img>
                            <img style={{ transform: "translateY(-50%) translateX(-50%)"}} className="w-[45%] block left-[50%] top-[50%] absolute" src="/images/dialogs/knocked-out-coin.svg"></img>
                        </div>
                        <p>Burning ASTO for THINK...</p>
                        {isProcessing && <div className="text-center my-4">Processing transaction, please wait...</div>}
                    </div>
                )}

                {!isProcessing && modalStage === 'approved' && (
                    <>
                        <p>
                            By continuing you agree to our <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">terms of service</a> and <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">privacy policy</a>.
                        </p>
                        <div className="pt-4">
                            <label htmlFor="asto-amount" className="text-sm text-gray-400">YOU ARE BURNING</label>
                            <ConversionInput tokenName="ASTO" tokenBalance={astoBurnDetails.astoBalanceToBurn} />
                            <label htmlFor="think-amount" className="text-sm text-gray-400">YOU ARE RECEIVING</label>
                            <ConversionInput tokenName="THINK" tokenBalance={astoBurnDetails.calculatedThinkToReceive} />
                            <br />ASTO approval successful!
                            <br />You can now proceed to burn your ASTO for $THINK.
                        </div>
                    </>
                )}
                {!isProcessing && modalStage === 'success' && (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_successful.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            ASTO burn successful! You have received $THINK.
                        </p>
                    </>
                )}
                {modalStage === 'error' && (
                    <div className="flex flex-col items-center justify-center">
                        <img src="/images/knocked_out_failed.svg" alt="think Logo" className="" />
                        {astoBurnDetails.errorMessage && <div className="text-red-500 mt-2">Error: {getFriendlyErrorMessage(astoBurnDetails.errorMessage)}</div>}
                    </div>
                )}
            </div>
        </>
    );

    const FooterComponent = () => (
        <div className="flex flex-col space-y-6 w-full">
            <div className="flex flex-row space-x-2">
                {modalStage === 'paused' && (
                    <ThinkDialogButton onClick={astoBurnDetails.onCancel}>
                        Approve ASTO
                    </ThinkDialogButton>
                )}
                {!isProcessing && modalStage === 'initial' && (
                    <ThinkDialogButton onClick={astoBurnDetails.onApprove}>
                        Approve ASTO
                    </ThinkDialogButton>
                )}
                {!isProcessing && modalStage === 'approved' && (
                    <ThinkDialogButton onClick={astoBurnDetails.onConfirmBurn}>
                        Confirm Burn
                    </ThinkDialogButton>
                )}
                {isProcessing && (
                    <ThinkDialogButton disabled={true} onClick={() => {}}>
                        Transaction in Progress
                    </ThinkDialogButton>
                )}
                {modalStage === 'success' || modalStage === 'error' ? (
                    <ThinkDialogButton onClick={astoBurnDetails.onCancel}>
                        Close
                    </ThinkDialogButton>
                ) : null}
            </div>
            {modalStage !== "initial" && astoBurnDetails.approvalTxHash && !astoBurnDetails.burnTxHash && (
                <p className="font-mono text-sm text-gray-400 text-center uppercase">
                    View Transaction 
                    &nbsp;{astoBurnDetails.explorerUrl ? <a href={`${astoBurnDetails.explorerUrl}/tx/${astoBurnDetails.approvalTxHash}`} target="_blank" rel="noopener noreferrer" className="underline text-blue-400">{truncateAddress(astoBurnDetails.approvalTxHash)}</a> : astoBurnDetails.approvalTxHash}.
                </p>
            )}
            {modalStage !== "initial" && astoBurnDetails.burnTxHash && (
                <p className="font-mono text-sm text-gray-400 text-center uppercase">
                    View Transaction 
                    &nbsp;{astoBurnDetails.explorerUrl ? <a href={`${astoBurnDetails.explorerUrl}/tx/${astoBurnDetails.burnTxHash}`} target="_blank" rel="noopener noreferrer" className="underline text-blue-400">{truncateAddress(astoBurnDetails.burnTxHash)}</a> : astoBurnDetails.approvalTxHash}.
                </p>
            )}
        </div>
    );


    return {
        TitleComponent,
        DescriptionComponent,
        FooterComponent,
    };
};

import { getFriendlyErrorMessage, formatNumberWithCommas, getExplorerUrl } from "@/components/ThinkTokenCard/TokenFunctions";
import { ClaimStakeStages, ThinkDialogButton, ThinkDialogContents } from "../ThinkDialog";
import { ClaimableDetails } from "../ClaimProvider";

export const ClaimStakeDialogContent: (obj: ClaimableDetails) => ThinkDialogContents = ({
    modalStage,
    isProcessing,
    totalThinkToClaim,
    errorMessage,
    txHash,
    totalClaimedTokens,
    onClose,
    submitClaim,
    submitClaimAndStake,
    reset,
}) => {

    const TitleComponent = () => (
        <>
            Claim and stake $THINK
        </>
    );

    const DescriptionComponent = () => {
        let description: React.ReactNode;
        switch (true) {
            case modalStage === 'paused':
                description = (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_unsuccessful.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            Claiming Disabled
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            Claiming is currently disabled. 
                        </p>
                        { errorMessage && (
                            <div className="font-mono text-red uppercase">Reach out to discord if you think this is an error.</div>
                        )}
                    </>
                );
                break;
            // TODO: modify the success entry to read the appropriate message after successful
            // TODO: poll a smart contract or something to get the stakeId to confirm staking occurred.
            case modalStage === 'success':
                description = (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_successful.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            Transaction Successfully sent
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            Your transaction is being processed. Please confirm {formatNumberWithCommas(totalClaimedTokens)} $THINK 
                            in the below transaction on Etherscan.
                        </p>
                    </>
                );
                break;
            case modalStage === 'error':
                description = (
                    <>
                        <img className="block w-[85%] m-auto py-4 pb-8" src="/images/logos/knocked_out_unsuccessful.svg"></img>
                        <p className="text-white text-2xl mb-2">
                            Transaction Unsuccessful
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            An error has occurred during the transaction. Please try again.
                        </p>
                        { errorMessage && (
                            <div className="font-mono text-red uppercase">Error: {getFriendlyErrorMessage(errorMessage)}</div>
                        )}
                    </>
                );
                break;
            case isProcessing:
                description = (
                    <>
                        <div className="block w-[65%] m-auto my-4 mb-8 relative">
                            <img className="block w-full opacity-0" src="/images/logos/knocked_out_coin_w_rays.svg"></img>
                            <img className="block w-full left-0 top-0 animate-circular absolute" src="/images/dialogs/knocked_out_coin_rays-only.png"></img>
                            <img style={{ transform: "translateY(-50%) translateX(-50%)"}} className="w-[45%] block left-[50%] top-[50%] absolute" src="/images/dialogs/knocked-out-coin.svg"></img>
                        </div>
                        <p className="text-white text-2xl mb-2">
                            {modalStage === 'initial' && "You are claiming all $THINK"}
                            {modalStage === 'preparing' && "Preparing Claim"}
                            {modalStage === 'claiming_staking' && "Claim and Stake Transaction in Progress"}
                            {modalStage === 'claiming' && "Claim Transaction in Progress"}
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            This Can take a few minutes. Please wait while the transaction completes.
                        </p>
                    </>
                );
                break;
            case modalStage === 'initial':
                description = (
                    <>
                        <img className="block w-[65%] m-auto py-4 pb-8" src="/images/logos/knocked_out_coin_w_rays.svg"></img>
                        <p className="text-white text-lg">
                            You are claiming all $THINK
                        </p>
                        <p className="font-mono text-gray-400 uppercase">
                            You will receive {formatNumberWithCommas(totalThinkToClaim)} $THINK.
                        </p>
                        <p className="font-mono text-red uppercase">
                            Caution: Claimed THINK will not transfer with future NFT and NFI sales.
                        </p>
                    </>
                );
                break;
        }

        return (
            <div className="text-center text-lg">
                {description}
            </div>
        )
    };

    const FooterComponent = () => {
        let action: React.ReactNode;
        switch (true) {
            case modalStage === 'paused':
                action = (
                    <ThinkDialogButton onClick={onClose}>
                        Close
                    </ThinkDialogButton>
                );
                break;
            case isProcessing:
                action = (
                    <ThinkDialogButton disabled={true} onClick={() => {}}>
                        in Progress
                    </ThinkDialogButton>
                );
                break;
            case modalStage === 'initial':
                action = (
                    <>
                        <ThinkDialogButton onClick={submitClaim}>
                            Claim (only)
                        </ThinkDialogButton>
                        <ThinkDialogButton onClick={submitClaimAndStake}>
                            Claim and Stake
                        </ThinkDialogButton>
                    </>
                );
                break;
            case modalStage === 'success':
                action = (
                    <ThinkDialogButton onClick={onClose}>
                        Close
                    </ThinkDialogButton>
                );
                break;
            case modalStage === 'error':
                action = (
                    <ThinkDialogButton onClick={reset}>
                        Try Again
                    </ThinkDialogButton>
                );
                break;
            default:
                action = (
                    <ThinkDialogButton disabled={true} onClick={() => { window.location.reload(); }}>
                        Error. Click to Reload
                    </ThinkDialogButton>
                );
                break;
        }

        return (
            <div className="flex flex-col space-y-6 w-full">
                <div className="flex flex-row space-x-2">
                    {action}
                </div>

                {(modalStage === "initial" || !txHash) && (
                    <p className="font-mono text-sm text-gray-400">
                        By continuing you agree to our <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">terms of service</a> and <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB]">privacy policy</a>.
                    </p>
                )}
                {modalStage !== "initial" && txHash && (
                    <p className="font-mono text-sm text-gray-400 text-center uppercase">
                        View Transaction on <a href={`${getExplorerUrl()}/tx/${txHash}`} target="_blank" rel="noopener noreferrer" className="underline text-[#22D0FB] hover:text-[#22D0FB]">Etherscan</a>
                    </p>
                )}
            </div>
        );
    }


    return {
        TitleComponent,
        DescriptionComponent,
        FooterComponent,
    };
};

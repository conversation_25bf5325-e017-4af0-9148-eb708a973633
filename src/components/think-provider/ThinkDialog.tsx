import { DialogDescription, DialogTitle } from "@radix-ui/react-dialog";
import { Dialog, DialogContent, DialogFooter, DialogHeader } from "../ui/dialog";
import { useEffect, useMemo, useState } from "react";
import Particles, { initParticlesEngine } from "@tsparticles/react";
import { loadImageShape } from "@tsparticles/shape-image";
import { loadConfettiPreset } from "@tsparticles/preset-confetti";


export interface ThinkDialogButtonProps {
  children: React.ReactNode;
  disabled?: boolean;
  theme?: 'primary' | 'secondary';
  onClick: () => void;
}

export interface ThinkDialogContents {
    TitleComponent: React.ComponentType<any>;
    DescriptionComponent: React.ComponentType<any>;
    FooterComponent: React.ComponentType<any>;
}


export type CommonModalStages = 'initial' | 'success' | 'paused' | 'error';
export type ClaimStakeStages = CommonModalStages | 'preparing' | 'claiming_staking' | 'claiming';
export type AstoConversionStages = CommonModalStages | 'approving' | 'approved' | 'burning' | 'allowance_check';
export type StakeStages = CommonModalStages | 'approving' | 'approved' | 'staking' | 'unstaking' | 'unstaked' | 'allowance_check';

export const ThinkDialogButton: React.FC<ThinkDialogButtonProps> = ({ children, theme = 'primary', disabled, onClick }) => {
    let buttonStyle = 'bg-gradient-to-b from-[#21D0FC] from-[23.44%] to-[#ECDCAA] to-[77.6%] hover:bg-[#21D0FC] text-black';
    if (theme === 'secondary') {
        buttonStyle = 'bg-[#000000] hover:bg-[#000000] from-[#000000] to-[#000000] text-white';
    }

    return (
        <button type="button" onClick={onClick} disabled={disabled} className={`
        inline-flex items-center justify-center gap-2 w-full whitespace-nowrap text-lg font-medium transition-colors h-9 px-4 py-2 !h-[55px] !px-2 rounded-full text-center
        ${buttonStyle}
        duration-300 disabled:bg-[#1B1B1B] disabled:cursor-not-allowed disabled:from-[#1B1B1B] disabled:to-[#1B1B1B]
    `}>
            {children}
        </button>
    );
};

interface ThinkDialogProps {
    open: boolean;
    isGoldenTicket?: boolean;
    onOpenChange: (isOpen: boolean) => void;
    TitleComponent: React.ComponentType<any>;
    DescriptionComponent: React.ComponentType<any>;
    FooterComponent: React.ComponentType<any>;
}

const ThinkDialog: React.FC<ThinkDialogProps> = ({ 
    open,
    isGoldenTicket,
    onOpenChange,
    TitleComponent, 
    DescriptionComponent, 
    FooterComponent,
}) => {
    const [init, setInit] = useState(false);

    // this should be run only once per application lifetime
    useEffect(() => {
        initParticlesEngine(async (engine) => {
            await loadImageShape(engine);
            await loadConfettiPreset(engine);
        }).then(() => {
            setInit(true);
        });
    }, []);

    const particlesLoaded = async (_?: any): Promise<void> => {
        // Doubly loaded and set init to true
        setInit(true);
    };

    const confettiNames = ["c-1","c-2","c-3","c-4","c-5","c-6","c-7","c-8"];
    const confettiImages = confettiNames.map((name) => ({
        src: `/images/confetti/${name}.svg`,
        name,
        width: 16,
        height: 16,
    }));
    const confettiImageNames = confettiNames.map((name) => ({name}));
    const options: any = useMemo(
        () => ({
            preload: confettiImages,
            fpsLimit: 120,
            emitters: {
                life: {
                    duration: 0,
                },
                position: {
                    "x": 50,
                    "y": 30
                },
            },
            preset: "confetti",
            particles: {
                rotate: {
                    animation: {
                        enable: true,
                        speed: 5,
                        sync: false,
                    },
                    direction: "random",
                    value: {
                        min: 0,
                        max: 1000,
                    },
                },
                shape: {
                    type: "image",
                    options: {
                        image: confettiImageNames
                    }
                },
                size: {
                    value: 16,
                },
            },
            detectRetina: true
        }),
        [],
    );

    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className={`max-h-screen overflow-y-auto text-white border-black-600 border-2 rounded-3xl p-8 ${isGoldenTicket ? " bg-gradient-to-b from-[#F6B259] from-[23.44%] to-[#EDDCAA] to-[65.6%]" : "bg-black"}`}>
            <DialogHeader>
                <DialogTitle className="text-2xl flex flex-row">
                    <TitleComponent />
                </DialogTitle>
                <DialogDescription asChild>
                    <div className="text-sm text-gray-400 pt-4">
                        <DescriptionComponent />
                    </div>
                </DialogDescription>
            </DialogHeader>
            <DialogFooter className="mt-4">
                <FooterComponent />
            </DialogFooter>
        </DialogContent>
        <div style={{zIndex: "205 !important"}}>
            {/* Add Confetti object here. */}
            {isGoldenTicket && open && init && (
                <Particles
                    id="confetti"
                    particlesLoaded={particlesLoaded}
                    options={options}
                    style={{zIndex: "205 !important"}}
                />
            )}
        </div>
      </Dialog>
    );
};

export default ThinkDialog;

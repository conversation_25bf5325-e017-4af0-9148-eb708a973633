import { useQuery } from "@tanstack/react-query";
import { useAuth } from "../auth/AuthContext";
import { getThinkDetails } from "@/lib/swap/think";
import { alchemy } from "@/lib/web3Config";
import { UseQueryResult } from "@tanstack/react-query";
import { createContext, useContext, useState } from "react";
import Big from "big.js";

export interface TokenDetails {
    tokenName: string;
    balance: Big;
    decimals: string;
    symbol: string;
    stakeAllowance: string;
    totalSupply: string;
}

export interface ThinkTokenContextType {
    thinkTokenDetailsQuery: UseQueryResult<any | undefined | null> | undefined;
    thinkTokenDetails: TokenDetails | undefined | null;
}

export const ThinkTokenContext = createContext({
    thinkTokenDetailsQuery: undefined,
    thinkTokenDetails: undefined,
} as ThinkTokenContextType);

export const useThinkToken = () => {
    return useContext(ThinkTokenContext);
};

export const ThinkTokenProvider = ({ ...props }: React.HTMLAttributes<HTMLDivElement>) => {
    const { user } = useAuth();

    if (!user || !user.address) {
        return null;
    }

    const thinkTokenDetailsQuery = useQuery({
        queryKey: ["thinkTokenDetails", user.address],
        queryFn: async () => {
            if (!user) return null;
            const results = await getThinkDetails(user);
            return results;
        },
        enabled: !!user,
        refetchOnWindowFocus: "always",
    });

    const context = {
        thinkTokenDetailsQuery,
        thinkTokenDetails: thinkTokenDetailsQuery.data,
    };

    return (
        <ThinkTokenContext.Provider value={context}>{props.children}</ThinkTokenContext.Provider>
    );
};

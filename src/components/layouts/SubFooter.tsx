import { cn } from "@/lib/utils";
import { NavLink } from "react-router";
import { PrimaryContainerComponent } from "./PrimaryContainerComponent";
import { WalletButton } from "../ui/wallet-button";
import { useTranslation } from "@/hooks/useTranslation";


function SubFooter({
    className
}: React.HTMLAttributes<HTMLDivElement>) {
    const { t } = useTranslation();

    return (
        <PrimaryContainerComponent className={cn("flex flex-col justify-center items-center py-10", className)}>
            <div className="text-off-white font-token uppercase leading-1 line-height-1 text-4xl lg:text-6xl xl:text-7xl 2xl:text-8xl text-center mb-4 max-w-sm md:max-w-2xl lg:max-w-4xl">{t('common.aiYouOwn')}</div>
            <div className="flex flex-row justify-center items-center gap-4">

                <WalletButton showLoginModal={false} className="px-10 py-8 bg-teal border-0 ring-teal text-black m-5 hover:bg-white" />
                {/* <NavLink to="#faq" className="rounded-full text-center font-semibold text-base md:text-base overflow-hidden lg:text-lg xl:text-xl px-6 md:px-7 lg:px-8 py-2 md:py-3 lg:py-4 ring ring-1 ring-inset ring-teal text-black text-center bg-teal !leading-[34px] hover:ring-0 hover:bg-gradient-to-b hover:from-teal hover:to-sand hover:text-black">Connect Wallet</NavLink> */}
            </div>
        </PrimaryContainerComponent>
    );
}

export { SubFooter };
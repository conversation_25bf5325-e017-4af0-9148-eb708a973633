import * as React from "react";
import { cn } from "@/lib/utils";
import { NavLink, useLocation } from "react-router";
import { MainNavComponentItem } from "../ui/MainNavComponentItem";
import { PrimaryContainerComponent } from "./PrimaryContainerComponent";
import { IconYoutube } from "../ui/IconYoutube";
import { IconX } from "../ui/IconX";
import { IconDiscord } from "../ui/IconDiscord";
import { IconMagicEden } from "../ui/IconMagicEden";
import { useTranslation } from "@/hooks/useTranslation";

const Footer = React.forwardRef<
    HTMLDivElement,
    React.ComponentProps<"footer">
>(({ className }, ref) => {
    const { t } = useTranslation();
    const location = useLocation();
    if (location.pathname.indexOf('/dashboard') !== -1) {
        return null; // Render nothing for the specified routes
    }

    return (
        <footer
            ref={ref}
            className={cn("Footer w-full flex flex-col justify-center items-center shrink-0", className)}
        >
            <PrimaryContainerComponent className="w-full py-20">
                <div className="flex flex-col md:flex-row md:items-start lg:justify-between w-full">
                    <div className="mb-8 md:mb-0 md:mr-16 flex-shrink-0 flex items-start">
                        <img src="/images/token-brain-fist.svg" className="text-black w-12 md:w-32 h-auto lg:w-48" />
                    </div>
                    <div className="flex flex-col md:flex-row md:flex-wrap md:items-start gap-8 md:gap-x-12 md:gap-y-8">
                        {/* About */}
                        <div className="flex flex-col">
                            <div className="font-bold text-lg md:text-xl mb-3">{t('footer.navigation.about')}</div>
                            <ul className="space-y-2">
                                <li><NavLink to="/about" className="hover:text-teal text-sm md:text-base" title={t('footer.navigation.team')}>{t('footer.navigation.team')}</NavLink></li>
                                <li><NavLink to="/investors" className="hover:text-teal text-sm md:text-base" title={t('footer.navigation.investors')}>{t('footer.navigation.investors')}</NavLink></li>
                                <li><NavLink to="/partners" className="hover:text-teal text-sm md:text-base" title={t('footer.navigation.partners')}>{t('footer.navigation.partners')}</NavLink></li>
                            </ul>
                        </div>
                        {/* THINK Protocol */}
                        <div className="flex flex-col">
                            <div className="font-bold text-lg md:text-xl mb-3">{t('footer.navigation.protocol')}</div>
                            <ul className="space-y-2">
                                <li><NavLink to="/claim" className="hover:text-teal text-sm md:text-base" title={t('footer.navigation.claim')}>{t('footer.navigation.claim')}</NavLink></li>
                                <li><NavLink to="/think-builders" className="hover:text-teal text-sm md:text-base" title={t('footer.navigation.builders')}>{t('footer.navigation.builders')}</NavLink></li>
                            </ul>
                        </div>
                        {/* Resources */}
                        <div className="flex flex-col">
                            <div className="font-bold text-lg md:text-xl mb-3">{t('footer.navigation.resources')}</div>
                            <ul className="space-y-2">
                                <li><NavLink to="https://docs.thinkagents.ai" className="hover:text-teal text-sm md:text-base" target="_blank" rel="noopener noreferrer" title={t('footer.navigation.docs')}>{t('footer.navigation.docs')}</NavLink></li>
                                <li><NavLink to="https://docs.thinkagents.ai/whitepaper" className="hover:text-teal text-sm md:text-base" target="_blank" rel="noopener noreferrer" title={t('footer.navigation.whitepaper')}>{t('footer.navigation.whitepaper')}</NavLink></li>
                                <li><NavLink to="https://docs.thinkagents.ai/faqs/faq" className="hover:text-teal text-sm md:text-base" title={t('footer.navigation.faq')}>{t('footer.navigation.faq')}</NavLink></li>
                            </ul>
                        </div>
                        {/* Follow Us */}
                        <div className="flex flex-col">
                            <div className="font-bold text-lg md:text-xl mb-3">{t('footer.navigation.followus')}</div>
                            <ul className="space-y-2">
                                <li><NavLink to="https://x.com/thinkagents" className="hover:text-teal text-sm md:text-base" target="_blank" rel="noopener noreferrer">X (Twitter) — EN</NavLink></li>
                                <li><NavLink to="https://x.com/thinkagents_cn" className="hover:text-teal text-sm md:text-base" target="_blank" rel="noopener noreferrer">X (Twitter) — CN</NavLink></li>
                                <li><NavLink to="https://discord.gg/thinkagents" className="hover:text-teal text-sm md:text-base" target="_blank" rel="noopener noreferrer">Discord</NavLink></li>
                                <li><NavLink to="https://www.youtube.com/@ThinkAgents" className="hover:text-teal text-sm md:text-base" target="_blank" rel="noopener noreferrer">YouTube</NavLink></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </PrimaryContainerComponent>
            <PrimaryContainerComponent className="w-full flex flex-col md:flex-row items-start md:items-center md:justify-between">
                <div className="text-2xs text-slate-200 font-mono flex flex-col md:flex-row gap-1 md:gap-6">
                    <NavLink to="/privacy-policy" title={t('footer.legal.privacy')} className="hover:text-teal">{t('footer.legal.privacy')}</NavLink>
                    <NavLink to="/terms-and-conditions" title={t('footer.legal.terms')} className="hover:text-teal">{t('footer.legal.terms')}</NavLink>
                </div>
                <div className="flex flex-row gap-4 font-semibold text-xl mt-4 md:mt-0 md:text-lg lg:text-xl mb-4 text-off-white">
                    <NavLink to="https://x.com/thinkagents" title={t('footer.socialMedia.twitter')} target="_blank" className="text-off-white hover:text-teal">
                        <IconX className="h-8" />
                    </NavLink>
                    <NavLink to="https://discord.gg/EjRsRrVc5F" title={t('footer.socialMedia.discord')} target="_blank" className="text-off-white hover:text-teal">
                        <IconDiscord className="h-8" />
                    </NavLink>
                    <NavLink to="https://www.youtube.com/@ThinkAgents" title={t('footer.socialMedia.youtube')} target="_blank" className="text-off-white hover:text-teal">
                        <IconYoutube className="h-8" />
                    </NavLink>
                    <NavLink to="https://magiceden.us/collections/ethereum/******************************************" title={t('footer.socialMedia.magicEden')} target="_blank" className="text-off-white hover:text-teal">
                        <IconMagicEden className="h-8" />
                    </NavLink>
                </div>
            </PrimaryContainerComponent>
            <div className="w-full mt-4 linear-gradient-teal-to-sand h-[5px]"></div>
        </footer>

    );
});
Footer.displayName = "Footer";

export { Footer };
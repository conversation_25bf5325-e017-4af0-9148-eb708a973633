import { cn } from "@/lib/utils";
import { NavLink, useLocation } from "react-router";
import { useNavigate } from "react-router";
import { useAuth } from "@/components/auth/AuthContext";
import { useState } from "react";


function DashboardLayout({
    className,
    ...props
}: React.HTMLAttributes<HTMLDivElement>) {
    const { innerWidth: windowWidth } = window;
    const [toggleSidebar, setToggleSidebar] = useState(windowWidth < 650 ? false : true);
    const { user, authLogout } = useAuth();
    const location = useLocation();
    const navigate = useNavigate();
    if (!user) {
        console.log("navigating")
        navigate('/');
    } else {
        return (
            <div className="flex flex-row">
                <div style={{ height: "calc(100vh - 100px)" }} className={`hidden xl:flex ${toggleSidebar ? "w-[250px] lg:w-[350px] lg:px-8 xl:px-10 2xl:px-16 3xl:px-24" : "w-[90px] lg:w-[110px] lg:px-8 xl:px-10 "} flex-col pt-10 px-8  text-mg flex flex-col justify-between items-start flex-grow dashboard-sidebar`}>

                    <div className="flex flex-col">
                        <NavLink to="/dashboard" className={`flex flex-row gap-6 items-left mb-6 hover:text-teal ${location.pathname === "/dashboard" ? "text-white" : "text-[#909090]"} hover:text-teal`}>
                            <img src="/images/brandmark-light-2.svg" className="w-auto h-6 sm:h-6" />

                            <span className={`${toggleSidebar ? "" : "hidden"} `}>
                                Dashboard
                            </span>
                        </NavLink>
                        {/* <NavLink to="/dashboard/token" className={`flex flex-row gap-6 items-left mb-6 ${location.pathname === "/dashboard/token" ? "text-white" : "text-[#909090]"} hover:text-teal`}>
                            <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-auto w-auto h-6 sm:h-6">
                                <path d="M2.13149 17.9937C1.8611 17.9919 1.59541 17.9229 1.35834 17.7929C1.12128 17.6628 0.920294 17.4758 0.773491 17.2487C0.624933 17.025 0.535067 16.7676 0.512173 16.5C0.48928 16.2324 0.534095 15.9634 0.642491 15.7177L1.69349 13.2647C0.656645 11.3754 0.336176 9.17549 0.790934 7.06889C1.24569 4.96229 2.44515 3.09046 4.16904 1.79711C5.89294 0.503769 8.02555 -0.124263 10.1753 0.0283428C12.325 0.180949 14.3475 1.10395 15.8714 2.62785C17.3953 4.15175 18.3183 6.17426 18.4709 8.32397C18.6235 10.4737 17.9955 12.6063 16.7021 14.3302C15.4088 16.0541 13.5369 17.2535 11.4303 17.7083C9.32375 18.1631 7.12381 17.8426 5.23449 16.8057L2.78149 17.8567C2.57641 17.9462 2.35523 17.9928 2.13149 17.9937ZM9.54649 1.62275C8.24365 1.62327 6.96449 1.97099 5.84068 2.6301C4.71686 3.28921 3.789 4.23591 3.15259 5.37274C2.51619 6.50956 2.19424 7.79545 2.21988 9.09804C2.24553 10.4006 2.61784 11.6728 3.29849 12.7837L3.51849 13.1427L2.13949 16.3597L5.35649 14.9807L5.71649 15.2007C6.6819 15.7923 7.77124 16.1522 8.89908 16.2521C10.0269 16.3521 11.1626 16.1893 12.2169 15.7767C13.2713 15.364 14.2158 14.7127 14.9762 13.8738C15.7366 13.0348 16.2922 12.0312 16.5996 10.9414C16.907 9.85169 16.9577 8.70556 16.7478 7.59294C16.5379 6.48032 16.073 5.43146 15.3897 4.52865C14.7064 3.62584 13.8232 2.89362 12.8094 2.38945C11.7956 1.88527 10.6787 1.62284 9.54649 1.62275ZM12.8055 11.3937H6.28949C6.07841 11.3863 5.87845 11.2973 5.73174 11.1454C5.58503 10.9934 5.50303 10.7905 5.50303 10.5792C5.50303 10.368 5.58503 10.1651 5.73174 10.0131C5.87845 9.8612 6.07841 9.77214 6.28949 9.76475H12.8055C12.9149 9.76091 13.0239 9.77914 13.126 9.81835C13.2282 9.85756 13.3214 9.91694 13.4001 9.99295C13.4789 10.069 13.5415 10.16 13.5842 10.2608C13.627 10.3615 13.649 10.4698 13.649 10.5792C13.649 10.6887 13.627 10.797 13.5842 10.8977C13.5415 10.9984 13.4789 11.0895 13.4001 11.1655C13.3214 11.2416 13.2282 11.3009 13.126 11.3401C13.0239 11.3793 12.9149 11.3976 12.8055 11.3937ZM8.73349 8.13775H6.28949C6.07841 8.13035 5.87845 8.0413 5.73174 7.88936C5.58503 7.73742 5.50303 7.53446 5.50303 7.32325C5.50303 7.11204 5.58503 6.90908 5.73174 6.75714C5.87845 6.6052 6.07841 6.51614 6.28949 6.50875H8.73349C8.94457 6.51614 9.14454 6.6052 9.29124 6.75714C9.43795 6.90908 9.51995 7.11204 9.51995 7.32325C9.51995 7.53446 9.43795 7.73742 9.29124 7.88936C9.14454 8.0413 8.94457 8.13035 8.73349 8.13775Z" fill="currentcolor" />
                            </svg>
                            <span className={`${toggleSidebar ? "" : "hidden"}`}>
                                Token
                            </span>
                        </NavLink> */}
                    </div>
                    <div className="shrink-0 space-y-3 mb-8 flex flex-col gap-y-1">
                        <NavLink onClick={() => authLogout()} to={location.pathname} className="flex flex-row gap-6 items-left mb-6 hover:text-teal text-[#909090] dashboard-sidebar-toggle ">
                            <div className="pl-6" />
                            <span className={`${toggleSidebar ? "" : "hidden"}`}>
                                Logout
                            </span>
                        </NavLink>
                        {/* <NavLink to="/dashboard/settings" className={`flex flex-row gap-6 items-left ${location.pathname === "/dashboard/settings" ? "text-white" : "text-[#909090]"} hover:text-teal`}>
                            <svg width="12" height="12" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="w-auto w-auto h-6 sm:h-6" >
                                <path d="M8.29803 18C8.07103 18 7.84603 17.992 7.63003 17.975C7.15478 17.9311 6.70886 17.7259 6.36633 17.3936C6.02379 17.0612 5.80526 16.6217 5.74703 16.148L5.67603 15.657C5.64147 15.5035 5.57222 15.36 5.47359 15.2375C5.37496 15.1149 5.24956 15.0166 5.10703 14.95C4.98303 14.883 4.86003 14.812 4.74203 14.738C4.55983 14.6194 4.34834 14.5536 4.13103 14.548C4.03324 14.5469 3.93616 14.5645 3.84503 14.6L3.38303 14.784C3.11882 14.8887 2.83723 14.9427 2.55303 14.943C2.22084 14.9485 1.89247 14.8716 1.59723 14.7193C1.30198 14.567 1.04906 14.3439 0.861026 14.07C0.609188 13.6998 0.385236 13.3114 0.191026 12.908C-0.00831652 12.4746 -0.0532307 11.986 0.0637571 11.5235C0.180745 11.061 0.45258 10.6525 0.834026 10.366L1.22503 10.056C1.34086 9.94993 1.43078 9.81866 1.48782 9.67232C1.54487 9.52598 1.56752 9.36848 1.55403 9.212C1.55403 9.149 1.55403 9.079 1.55403 9C1.55403 8.921 1.55403 8.851 1.55403 8.788C1.56752 8.63152 1.54487 8.47402 1.48782 8.32768C1.43078 8.18134 1.34086 8.05007 1.22503 7.944L0.834026 7.634C0.452729 7.34738 0.181009 6.93889 0.0640331 6.47645C-0.0529429 6.014 -0.00812899 5.52545 0.191026 5.092C0.385069 4.6885 0.609027 4.30008 0.861026 3.93C1.04906 3.6561 1.30198 3.43303 1.59723 3.2807C1.89247 3.12836 2.22084 3.05151 2.55303 3.057C2.83723 3.05734 3.11882 3.11128 3.38303 3.216L3.84503 3.4C3.93616 3.43545 4.03324 3.4531 4.13103 3.452C4.34834 3.44636 4.55983 3.38059 4.74203 3.262C4.86003 3.188 4.98303 3.116 5.10703 3.05C5.24965 2.98356 5.37512 2.88526 5.47377 2.76269C5.57242 2.64011 5.64161 2.49653 5.67603 2.343L5.74703 1.853C5.80519 1.37908 6.02365 0.939287 6.36615 0.606606C6.70865 0.273924 7.15461 0.0683519 7.63003 0.024C7.85003 0.008 8.07403 0 8.29803 0C8.52203 0 8.74703 0.008 8.96703 0.024C9.44224 0.0681685 9.88805 0.273575 10.2304 0.606104C10.5728 0.938633 10.791 1.37827 10.849 1.852L10.921 2.342C10.955 2.49558 11.0239 2.63926 11.1224 2.76188C11.2209 2.8845 11.3464 2.98275 11.489 3.049C11.608 3.112 11.728 3.182 11.855 3.261C12.0372 3.37959 12.2487 3.44536 12.466 3.451C12.5641 3.45216 12.6616 3.43451 12.753 3.399L13.214 3.215C13.4783 3.11035 13.7598 3.05641 14.044 3.056C14.3762 3.0506 14.7045 3.12749 14.9998 3.27982C15.295 3.43214 15.5479 3.65517 15.736 3.929C15.9887 4.29902 16.2133 4.68744 16.408 5.091C16.6072 5.52445 16.652 6.013 16.535 6.47545C16.418 6.93789 16.1463 7.34638 15.765 7.633L15.373 7.943C15.2572 8.04907 15.1674 8.18037 15.1106 8.32673C15.0537 8.47309 15.0313 8.63058 15.045 8.787C15.045 8.857 15.045 8.928 15.045 8.999C15.045 9.07 15.045 9.141 15.045 9.211C15.0313 9.36742 15.0537 9.52491 15.1106 9.67127C15.1674 9.81763 15.2572 9.94893 15.373 10.055L15.765 10.365C16.1465 10.6515 16.4183 11.06 16.5353 11.5225C16.6523 11.985 16.6074 12.4736 16.408 12.907C16.2132 13.3105 15.9886 13.6989 15.736 14.069C15.5479 14.3428 15.295 14.5659 14.9998 14.7182C14.7045 14.8705 14.3762 14.9474 14.044 14.942C13.7598 14.9416 13.4783 14.8876 13.214 14.783L12.753 14.599C12.6616 14.5635 12.5641 14.5458 12.466 14.547C12.2487 14.5526 12.0372 14.6184 11.855 14.737C11.727 14.817 11.608 14.886 11.489 14.949C11.3465 15.0154 11.2212 15.1137 11.1227 15.2363C11.0242 15.3589 10.9552 15.5025 10.921 15.656L10.849 16.147C10.7912 16.6207 10.5729 17.0603 10.2305 17.3927C9.88811 17.7251 9.44222 17.9302 8.96703 17.974C8.75103 17.992 8.52603 18 8.29803 18ZM4.12503 13.046C4.62383 13.0535 5.11094 13.1981 5.53303 13.464C5.64603 13.533 5.73303 13.584 5.81703 13.628C6.16783 13.8034 6.4723 14.059 6.70567 14.3742C6.93903 14.6894 7.09471 15.0553 7.16003 15.442L7.23103 15.932C7.24124 16.0673 7.29704 16.1952 7.3893 16.2947C7.48157 16.3943 7.60485 16.4596 7.73903 16.48C7.91903 16.493 8.10703 16.5 8.29803 16.5C8.48903 16.5 8.67703 16.493 8.85703 16.48C8.9912 16.4596 9.11448 16.3943 9.20675 16.2947C9.29901 16.1952 9.35481 16.0673 9.36503 15.932L9.43603 15.442C9.50153 15.0552 9.65738 14.6893 9.89092 14.3741C10.1245 14.0589 10.4291 13.8033 10.78 13.628C10.871 13.579 10.96 13.528 11.065 13.464C11.4867 13.198 11.9735 13.0534 12.472 13.046C12.7583 13.0452 13.0421 13.0991 13.308 13.205L13.769 13.389C13.856 13.4246 13.949 13.4433 14.043 13.444C14.131 13.4469 14.2184 13.4284 14.2976 13.39C14.3769 13.3517 14.4456 13.2947 14.498 13.224C14.7082 12.9157 14.8951 12.5921 15.057 12.256C15.1076 12.1302 15.1133 11.9909 15.0733 11.8614C15.0332 11.732 14.9498 11.6202 14.837 11.545L14.445 11.234C14.1424 10.9848 13.9031 10.6675 13.7467 10.3081C13.5903 9.94864 13.5211 9.55727 13.545 9.166V9V8.834C13.5212 8.44259 13.5904 8.0511 13.7468 7.69152C13.9032 7.33193 14.1425 7.01445 14.445 6.765L14.837 6.455C14.9499 6.37962 15.0333 6.2677 15.0734 6.13804C15.1135 6.00839 15.1077 5.8689 15.057 5.743C14.8951 5.40686 14.7082 5.08329 14.498 4.775C14.4456 4.70399 14.3767 4.64678 14.2973 4.60828C14.2178 4.56978 14.1302 4.55116 14.042 4.554C13.9484 4.55479 13.8557 4.57346 13.769 4.609L13.308 4.794C13.0417 4.8998 12.7576 4.95376 12.471 4.953C11.9728 4.94577 11.4863 4.80112 11.065 4.535C10.975 4.481 10.878 4.424 10.78 4.37C10.4291 4.19516 10.1245 3.9399 9.89094 3.62502C9.65738 3.31014 9.50151 2.94454 9.43603 2.558L9.36503 2.068C9.35584 1.93242 9.30037 1.80411 9.2079 1.70453C9.11543 1.60496 8.99156 1.54017 8.85703 1.521C8.67703 1.508 8.48903 1.501 8.29803 1.501C8.10703 1.501 7.91903 1.508 7.73903 1.521C7.60449 1.54017 7.48063 1.60496 7.38815 1.70453C7.29568 1.80411 7.24021 1.93242 7.23103 2.068L7.16003 2.558C7.09497 2.94455 6.9395 3.31026 6.70629 3.62532C6.47308 3.94038 6.16872 4.19588 5.81803 4.371C5.73303 4.416 5.64303 4.471 5.53403 4.536C5.11238 4.80206 4.62554 4.94669 4.12703 4.954C3.84073 4.95422 3.55701 4.89992 3.29103 4.794L2.83103 4.609C2.74433 4.57353 2.65169 4.55487 2.55803 4.554C2.46979 4.55107 2.38217 4.56966 2.30272 4.60816C2.22328 4.64667 2.1544 4.70393 2.10203 4.775C1.89153 5.08328 1.70434 5.40685 1.54203 5.743C1.49195 5.86897 1.48646 6.00828 1.52648 6.13779C1.5665 6.26731 1.64962 6.37924 1.76203 6.455L2.15403 6.765C2.45671 7.01434 2.69606 7.33181 2.85249 7.69142C3.00892 8.05103 3.07798 8.44257 3.05403 8.834V9V9.166C3.07806 9.55729 3.00904 9.94871 2.8526 10.3082C2.69616 10.6676 2.45676 10.9849 2.15403 11.234L1.76203 11.545C1.64955 11.6205 1.56636 11.7322 1.52632 11.8616C1.48629 11.991 1.49183 12.1302 1.54203 12.256C1.70418 12.5922 1.89137 12.9158 2.10203 13.224C2.15435 13.2948 2.2231 13.3518 2.30237 13.3902C2.38164 13.4285 2.46903 13.447 2.55703 13.444C2.65102 13.4432 2.74401 13.4245 2.83103 13.389L3.29103 13.205C3.5564 13.0995 3.83945 13.0455 4.12503 13.046ZM8.29803 12.746C7.55618 12.746 6.83099 12.526 6.21422 12.1137C5.59745 11.7015 5.11681 11.1156 4.8331 10.4301C4.54939 9.74468 4.47536 8.99048 4.62038 8.26294C4.7654 7.5354 5.12295 6.86722 5.64779 6.34294C6.17264 5.81865 6.8412 5.46181 7.56889 5.31757C8.29658 5.17333 9.05071 5.24816 9.73586 5.5326C10.421 5.81704 11.0064 6.29831 11.418 6.91552C11.8296 7.53273 12.0488 8.25815 12.048 9C12.047 9.99424 11.6515 10.9474 10.9485 11.6505C10.2455 12.3535 9.29226 12.7489 8.29803 12.75V12.746ZM8.29803 6.746C7.85319 6.746 7.41833 6.87786 7.04841 7.12492C6.67849 7.37197 6.3901 7.72314 6.21968 8.13404C6.04927 8.54494 6.00448 8.99714 6.09097 9.43348C6.17746 9.86983 6.39136 10.2708 6.70563 10.5856C7.01989 10.9004 7.42043 11.115 7.85663 11.2023C8.29282 11.2895 8.74509 11.2456 9.1563 11.0759C9.5675 10.9062 9.91918 10.6184 10.1669 10.2489C10.4146 9.87946 10.5472 9.44484 10.548 9C10.5472 8.40351 10.3099 7.83167 9.88814 7.40989C9.46635 6.9881 8.89452 6.75079 8.29803 6.75V6.746Z" fill="currentcolor" />
                            </svg>
                            <span className={`${toggleSidebar ? "" : "hidden"} ${location.pathname === "/dashboard/settings" ? "text-white" : "text-[#909090]"} hover:text-teal `}>
                                Settings
                            </span>
                        </NavLink> */}
                        <NavLink onClick={() => setToggleSidebar(!toggleSidebar)} to={location.pathname} className="flex flex-row gap-6 items-left mb-6 hover:text-teal text-[#909090] dashboard-sidebar-toggle ">
                            <svg width="12" height="12" viewBox="0 0 14 16" fill="none" xmlns="http://www.w3.org/2000/svg" className={`${toggleSidebar ? "" : "rotate-180"} w-auto w-auto h-4 sm:h-6`} >
                                <path d="M1.00014 1.14307V14.8574M4.97281 8.89263L10.5719 13.3719C11.3202 13.9706 12.4287 13.4378 12.4287 12.4795V3.52092C12.4287 2.56263 11.3202 2.02986 10.5719 2.6285L4.97281 7.10779C4.40092 7.5653 4.40092 8.43512 4.97281 8.89263Z" stroke="currentcolor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            <span className={`${toggleSidebar ? "" : "hidden"}`}>
                                Collapse
                            </span>
                        </NavLink>
                    </div>
                </div>
                <div className="flex-col w-full xl:rounded-tl-2xl overflow-hidden">
                    {props.children}
                </div>
            </div>
        );
    }
}

export { DashboardLayout };
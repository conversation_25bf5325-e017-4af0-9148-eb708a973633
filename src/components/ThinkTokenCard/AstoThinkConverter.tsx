import React, { useEffect } from 'react';
import { truncateAddress, calculateAstoThink, calculateAmountFromDecimal, getExplorerUrl } from './TokenFunctions';
import Big from 'big.js';
import { useSendUserOperation, useSmartAccountClient } from "@account-kit/react";
import { addAllowanceTransaction, getAstoDetails } from "@/lib/swap/asto";
import { getSwapDetails, swapTokenTx } from "@/lib/swap/swap";
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/components/auth/AuthContext';
import ThinkDialog, { AstoConversionStages } from '../think-provider/ThinkDialog';
import { AstoConverterDialogContent } from '../think-provider/dialogs/AstoConversion';
import RootNetworkAstoBurn from './RootNetworkAstoBurn';
import { useThinkToken } from '../think-provider/ThinkTokenProvider';
import { AstoTokenDetails, useAstoToken } from '../think-provider/AstoTokenProvider';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { IconIndicator } from '../ui/IconIndicator';
import { ASTO_SWAP_ENDS } from '@/time-configs/TGE-configs';

export const MINIMUM_SWAP_AMOUNT = 0.000001;

// Countdown Component
const AstoCountdown: React.FC = () => {
  const [timeLeft, setTimeLeft] = React.useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0
  });

  React.useEffect(() => {
    // Set target date - you can modify this to your desired end date
    const targetDate = ASTO_SWAP_ENDS.getTime();

    const updateCountdown = () => {
      const now = new Date().getTime();
      const difference = targetDate - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 1000);

    return () => clearInterval(interval);
  }, []);

  const formatTime = (time: number) => time.toString().padStart(2, '0');

  return (
    <span className="text-white font-mono">
      {timeLeft.days}d {formatTime(timeLeft.hours)}h {formatTime(timeLeft.minutes)}m {formatTime(timeLeft.seconds)}s
    </span>
  );
};

interface AstoConversionCardProps { }

export const AstoConversionCard: React.FC<AstoConversionCardProps> = () => {
  const { user } = useAuth();
  const walletAddress = user?.address;
  const { client } = useSmartAccountClient({ type: "LightAccount" });
  const { thinkTokenDetailsQuery, thinkTokenDetails: thinkTokenData } = useThinkToken();
  const { astoTokenDetailsQuery, astoDetails: astoTokenData } = useAstoToken();
  const astoDetails = astoTokenData;
  const thinkDetails = thinkTokenData;

  let swapDetails = useQuery({
    queryKey: ["swapDetails"],
    queryFn: async () => {
      const result = await getSwapDetails();
      return result;
    },
    retry: 1,
    refetchOnWindowFocus: "always",
  });
  const swapperData = swapDetails && swapDetails.data;

  const checkInitialStage = (details?: AstoTokenDetails | null) => {
    let initialStage: AstoConversionStages = "initial";
    if (!details) return initialStage;
    const totalAllowance = calculateAmountFromDecimal(BigInt(details.allowanceForSwap), BigInt(details.decimals));
    if (swapDetails.data && swapDetails.data.paused) {
      initialStage = "paused";
    } else if (new Big(totalAllowance).gte(details.balance) && details.balance.gt(new Big(MINIMUM_SWAP_AMOUNT))) {
      initialStage = "approved";
    }
    return initialStage;
  }

  const [modalStage, setModalStage] = React.useState<AstoConversionStages>(checkInitialStage(astoTokenData));
  const modalStageRef = React.useRef(modalStage); // Ref to access current stage in callbacks
  const [approvalTxHash, setApprovalTxHash] = React.useState<string | null>(null);
  const [burnTxHash, setBurnTxHash] = React.useState<string | null>(null);
  const [currentError, setCurrentError] = React.useState<string | null>(null);
  const [intervalCheckId, setIntervalCheckId] = React.useState<NodeJS.Timeout | undefined>();
  const [attemptsToConfirm, setAttemptsToConfirm] = React.useState<number>(0);

  const { sendUserOperation, isSendingUserOperation } = useSendUserOperation({
    client: client,
    waitForTxn: true,
    onSuccess: ({ hash }: { hash: string }) => {
      if (modalStageRef.current === 'approving') {
        setApprovalTxHash(hash);
        setCurrentError(null);
      } else if (modalStageRef.current === 'burning') {
        setBurnTxHash(hash);
        setCurrentError(null);
      }
    },
    onError: (error: any) => {
      console.error("Transaction failed:", error);
      setCurrentError(error.message || "An unknown error occurred during the transaction.");
      setModalStage('error');
    },
  });

  const swappableThink = astoTokenData &&
    thinkTokenData &&
    astoTokenData.balance &&
    astoTokenData.totalSupply &&
    thinkTokenData.totalSupply &&
    astoTokenData.totalSupply !== "0" &&
    thinkTokenData.totalSupply !== "0"
    ? calculateAstoThink(`${astoTokenData.balance}`, astoTokenData.totalSupply, thinkTokenData.totalSupply, 4)
    : "0";


  const astoAmountToBurn = astoTokenData && astoTokenData.balance ? astoTokenData.balance.toString() : "0";

  const truncatedWalletAddress = walletAddress ? truncateAddress(walletAddress) : "Connecting wallet...";

  const [isBurnModalOpen, setBurnModalOpen] = React.useState(false);

  useEffect(() => {
      if (swapperData?.paused.data) {
          setModalStage('paused');
      } 
  }, [swapperData?.paused]);

  React.useEffect(() => {
    modalStageRef.current = modalStage;

    if (modalStage === "approving") {
      const intervalId = setInterval(() => {
        astoTokenDetailsQuery?.refetch(); // Refetch asto token
        setAttemptsToConfirm(attemptsToConfirm + 1);
      }, 5000);
      setIntervalCheckId(intervalId);
    } else if (modalStage === "burning") {
      const intervalId = setInterval(() => {
        astoTokenDetailsQuery?.refetch(); // Refetch asto token
        setAttemptsToConfirm(attemptsToConfirm + 1);
      }, 5000);
      setIntervalCheckId(intervalId);
    }
  }, [modalStage]);

  // if the token allowance has changed, check the approval process.
  useEffect(() => {
    if (modalStage === "approving" && checkInitialStage(astoTokenData) === "approved") {
      setModalStage("approved");
      if (intervalCheckId !== undefined) clearInterval(intervalCheckId)
      setAttemptsToConfirm(0);
      setIntervalCheckId(undefined);
    } else if (attemptsToConfirm >= 60) {
      setModalStage("error");
      setCurrentError("Taking longer than expected to confirm allowance. Try refreshing the page or come back later. Contact support if the issue persists.");
      setAttemptsToConfirm(0);
      if (intervalCheckId !== undefined) clearInterval(intervalCheckId)
      setIntervalCheckId(undefined);
    }

    if (modalStage === "initial") {
      const initialStage = checkInitialStage(astoTokenData);
      if (initialStage === "approved") {
        setModalStage("approved");
      }
    }
  }, [astoTokenData?.allowanceForSwap]);

  // if the token balance has changed
  useEffect(() => {
    // assuming that if the balance is below the minimum and burning, then it succeeded.
    const noLongerMeetsMinimum = astoTokenData ? astoTokenData?.balance < new Big(MINIMUM_SWAP_AMOUNT) : false;
    if (modalStage === "burning" && noLongerMeetsMinimum) {
      setModalStage("success");
      if (intervalCheckId !== undefined) clearInterval(intervalCheckId)
      setAttemptsToConfirm(0);
      setIntervalCheckId(undefined);
      thinkTokenDetailsQuery?.refetch(); // Refetch think token
    } else if (attemptsToConfirm >= 60) {
      setModalStage("error");
      setCurrentError("Taking longer than expected to confirm allowance. Try refreshing the page or come back later. Contact support if the issue persists.");
      setAttemptsToConfirm(0);
      if (intervalCheckId !== undefined) clearInterval(intervalCheckId)
      setIntervalCheckId(undefined);
    }
  }, [astoTokenData?.balance]);

  const openBurnModal = () => {
    setModalStage(checkInitialStage(astoTokenData));
    setApprovalTxHash(null);
    setBurnTxHash(null);
    setCurrentError(null);
    setBurnModalOpen(true);
  };

  const handleModalCancel = () => {
    setBurnModalOpen(false);
    if (modalStageRef.current !== 'success' && modalStageRef.current !== 'error') {
      setModalStage(checkInitialStage(astoTokenData)); // Reset if cancelled mid-process
    }
  };

  const handleApprove = async () => {
    console.log("smartAccountClient", client);
    console.log("user", user);
    console.log("astoDetails", astoDetails);
    console.log("astoAmountToBurn", astoAmountToBurn);
    if (!user || !astoDetails || parseFloat(astoAmountToBurn) <= 0) {
      setCurrentError("Cannot proceed: Missing user data, ASTO details, or invalid amount.");
      setModalStage('error');
      return;
    }
    const currentAstoDecimals = Number(astoDetails.decimals);
    if (isNaN(currentAstoDecimals)) {
      setCurrentError("Invalid ASTO decimals configuration.");
      setModalStage('error');
      return;
    }

    setModalStage('approving');
    setCurrentError(null);

    try {
      const amountToApproveInBaseUnits = new Big(astoAmountToBurn).mul(new Big(10).pow(currentAstoDecimals)).toFixed(0);
      console.log("amountToApproveInBaseUnits", amountToApproveInBaseUnits);
      const approveTx = addAllowanceTransaction(amountToApproveInBaseUnits);
      sendUserOperation({ uo: { ...approveTx } });
    } catch (err: any) {
      console.error("Approval setup error:", err);
      setCurrentError(err.message || "Failed to initiate approval.");
      setModalStage('error');
    }
  };

  const handleConfirmBurn = async () => {
    if (!user || !astoDetails || !thinkDetails || new Big(astoAmountToBurn) <= new Big(MINIMUM_SWAP_AMOUNT)) {
      setCurrentError("Cannot proceed: Missing required data or invalid amount.");
      setModalStage('error');
      return;
    }
    const currentAstoDecimals = Number(astoDetails.decimals);
    if (isNaN(currentAstoDecimals)) {
      setCurrentError("Invalid ASTO decimals configuration for burn.");
      setModalStage('error');
      return;
    }

    setModalStage('burning');
    setCurrentError(null);

    try {
      const amountToBurnInBaseUnits = new Big(astoAmountToBurn).mul(new Big(10).pow(currentAstoDecimals)).toFixed(0);
      console.log("amountToBurnInBaseUnits", amountToBurnInBaseUnits);
      const burnTx = swapTokenTx(amountToBurnInBaseUnits);
      sendUserOperation({ uo: { ...burnTx } });
    } catch (err: any) {
      console.error("Burn setup error:", err);
      setCurrentError(err.message || "Failed to initiate burn.");
      setModalStage('error');
    }
  };

  return (
    <div className="mt-8 p-6 bg-[#190A31] border border-[#2D1058] rounded-xl shadow-lg">
      <div className="flex items-center mb-3">
        <img src="/images/logos/asto-icon.png" alt="ASTO Logo" className="w-7 h-7 mr-3" />
        <h3 className="text-lg font-bold text-white font-token">
          $ASTO Holders
          <Tooltip delayDuration={0}>
            <TooltipTrigger onClick={(e) => e.preventDefault()}>
              <span className="pl-2 inline-block"><IconIndicator /></span>
            </TooltipTrigger>
            <TooltipContent
                side="top"
                align="center"
            >
              <div className="flex items-center flex-col justify-end -mb-2">
                <div className="transform origin-bottom-right bg-neutral-700 border-2 border-neutral-700 text-base text-white p-2 rounded-lg max-w-[400px] flex-1">
                  Asto holders can convert their ASTO tokens to THINK tokens. There is a limited time to do so.
                </div>
                <div className="ml-3 w-5 overflow-hidden">
                  <div className="h-3 bg-neutral-700 -rotate-45 transform origin-top-left rounded-sm"></div>
                </div>
              </div>
            </TooltipContent>
          </Tooltip>

        </h3>
      </div>
      <p className="mb-6 text-sm text-[#C5ABED] leading-relaxed font-mono">
        Congratulations! You have $ASTO to burn and convert to $THINK.
        Not sure what this means? Learn more <a href="#" className="underline text-[#22D0FB] hover:text-[#22D0FB] font-semibold">here</a>.
      </p>
      {/* ASTO eth network Balance */}
      <div className="lg:grid lg:grid-cols-3 items-center gap-x-4">
        <div className="flex items-center col-span-1">
          <img src="/images/logos/eth-icon.png" alt="ASTO" className="w-9 h-9 mr-2.5 rounded-full bg-purple-900 p-1" />
          <div>
            <p className="font-semibold text-white text-sm font-mono">$ASTO</p>
            <p className="text-xs text-purple-300 font-mono">{truncatedWalletAddress}</p>
          </div>
        </div>
        <div className="ml-11 lg:ml-0 col-span-1 text-left">
          <p className="font-semibold text-white text-sm font-mono">{astoAmountToBurn} ASTO</p>
          <p className="text-xs text-purple-300 font-mono">{swappableThink} $THINK</p>
        </div>
        <div className="lg:col-span-1 text-right flex justify-end">
          <button
            onClick={openBurnModal}
            className="bg-[#854BD9] hover:bg-[#2D1058] text-white uppercase font-semibold py-1 px-2 rounded-md text-sm transition-colors duration-150 shadow-md hover:shadow-lg flex items-center gap-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
              <path d="M12.3998 9.30003C12.3998 11.8527 10.8289 12.9729 9.61507 13.3506C9.35623 13.4312 9.18611 13.1294 9.34095 12.9069C9.86919 12.1478 10.4798 10.9895 10.4798 9.90003C10.4798 8.72967 9.49336 7.34793 8.72308 6.49569C8.54697 6.30085 8.2398 6.42977 8.2302 6.69223C8.19838 7.56175 8.06117 8.72509 7.4695 9.63684C7.37426 9.78359 7.1721 9.79576 7.06359 9.65853C6.87872 9.42473 6.69385 9.13591 6.50899 8.90775C6.40941 8.78485 6.22943 8.78316 6.11457 8.89191C5.66676 9.31594 5.0398 9.97716 5.0398 10.8C5.0398 11.3936 5.32268 12.1324 5.61427 12.7045C5.74669 12.9643 5.50103 13.269 5.24866 13.1229C4.14715 12.4853 2.7998 11.1642 2.7998 9.30003C2.7998 7.41216 5.38598 4.79692 6.37344 2.32617C6.52925 1.9363 7.00946 1.7531 7.34345 2.00752C9.36617 3.54836 12.3998 6.5269 12.3998 9.30003Z" stroke="#F8F8F8" strokeWidth="1.2" />
            </svg>
            Burn
          </button>
        </div>
      </div>
      <RootNetworkAstoBurn />

      {/* ASTO Conversion Countdown */}
      <div className="mt-6 w-full px-4 py-1 border border-[#2D1058] rounded-lg">
        <div className="text-center">
          <p className="text-[#C5ABED] font-semibold text-sm">
            $ASTO CONVERSION ENDS <AstoCountdown />
          </p>
        </div>
      </div>

      <ThinkDialog
        open={isBurnModalOpen}
        onOpenChange={(isOpen) => { if (!isOpen) handleModalCancel(); else setBurnModalOpen(true); }}
        {...AstoConverterDialogContent({
          modalStage,
          isProcessing: isSendingUserOperation || modalStage === 'approving' || modalStage === 'burning',
          astoBurnDetails: {
            astoBalanceToBurn: astoAmountToBurn,
            calculatedThinkToReceive: swappableThink,
            approvalTxHash,
            burnTxHash,
            explorerUrl: getExplorerUrl(),
            errorMessage: currentError,
            onApprove: handleApprove,
            onConfirmBurn: handleConfirmBurn,
            onCancel: handleModalCancel,
          },
        })}
      />
    </div>
  );
};

export default AstoConversionCard;
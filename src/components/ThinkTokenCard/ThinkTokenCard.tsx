import React, { use, useEffect, useState } from 'react';
import { useAuth } from '@/components/auth/AuthContext';
import { NftAccordion } from './NftAccordion';
import { useQuery } from "@tanstack/react-query";
import { apiClient, isGetWalletResponse } from "@/lib/api";
import { formatNumberWithCommas, truncateAddress } from './TokenFunctions';
import { AstoConversionCard } from './AstoThinkConverter';
import { TooltipProvider } from '../ui/tooltip';
import ThinkDialog from '../think-provider/ThinkDialog';
import { ClaimStakeDialogContent } from '../think-provider/dialogs/ClaimStake';
import { GoldenTicketDialogContent } from '../think-provider/dialogs/GoldenTicket';
import { AstoTokenProvider, useAstoToken } from '../think-provider/AstoTokenProvider';
import { useClaim } from '../think-provider/ClaimProvider';
import { useStake } from '../think-provider/StakeProvider';
import { useThinkToken } from '../think-provider/ThinkTokenProvider';
import { StakeDialogContent } from '../think-provider/dialogs/Stake';
import Big from 'big.js';

// Interface for AddedWalletsCard props
interface AddedWallet {
  id: number;
  address: string;
  networkName: string;
  thinkAmount: number; // Retaining for now, though not displayed in the compact view
  imageUrl: string;
  primary?: boolean; // From the example data
}

interface AddedWalletsCardProps {
  wallets: AddedWallet[];
  onAddNewWallet: () => void;
}

// Added Wallets Card Component
const AddedWalletsCard: React.FC<AddedWalletsCardProps> = ({
  wallets,
  onAddNewWallet,
}) => {
  return (
    <div className="mt-8 p-4 bg-[#20232B] rounded-xl shadow-lg">
      <h3 className="text-lg font-bold text-white">Added Wallets</h3>
      <p className="text-xs text-[#9D9FA1] leading-relaxed">
        Add wallets to see how much $THINK you can claim. Once you are set, determine which wallet you wish to stake your $THINK
      </p>
      <div className="mt-4 flex gap-4 flex-wrap">
        {wallets.map((wallet) => (
          <div key={wallet.id} className="bg-black rounded-full shadow-lg py-1 px-2">
            <div className="flex items-center">
              <img src={wallet.imageUrl} alt={wallet.networkName} className="w-9 h-9 mr-2.5 rounded-full bg-purple-900 p-1" />
              <p className="font-semibold text-white text-sm">{truncateAddress(wallet.address)}</p>
            </div>
          </div>
        ))}
      </div>
      <div className="mt-4">
        <button
          type="button"
          onClick={onAddNewWallet}
          className="
          inline-flex items-center justify-center gap-2 w-full whitespace-nowrap text-lg text-white font-medium transition-colors h-9 px-4 py-2 !h-[55px] !px-2 rounded-full text-center
          bg-black hover:bg-neutral-800
        ">
          + Add New Wallet
        </button>
      </div>
    </div>
  );
};

// Interface for ThinkTokenCard props (currently empty but good for structure)
interface ThinkTokenCardProps { }

// Main ThinkTokenCard Component
const ThinkTokenCard: React.FC<ThinkTokenCardProps> = () => {
  const { user } = useAuth();
  const walletAddress = user?.address;
  const [isClaimModalOpen, setIsClaimModalOpen] = useState(false);
  const [isStakeModalOpen, setIsStakeModalOpen] = useState(false);
  const [goldenTicketOpen, setGoldenTicketOpen] = useState(false);
  const {
    claimableResponse,
    totalClaimableTokens,
    totalClaimedTokens,
    claimableQuery,
    claimDetails,
    claimHasGoldenTicket,
    goldenTicketAmount,
  } = useClaim();
  const {
    thinkTokenDetails,
  } = useThinkToken();
  const {
    stakeDetails,
  } = useStake();


  // alter modal for golden ticket if claim has golden ticket.
  useEffect(() => {
    if (claimHasGoldenTicket && isClaimModalOpen) {
      setGoldenTicketOpen(true);
    }
  }, [claimHasGoldenTicket]);

  const openClaimModal = (isOpen: boolean) => {
    if (claimHasGoldenTicket && isOpen) {
      setGoldenTicketOpen(true);
      setIsClaimModalOpen(isOpen);
    } else {
      setIsClaimModalOpen(isOpen);
    }
  }

  const handleAddNewWallet = () => {
    alert("Add New Wallet functionality to be implemented!");
  };

  // Fetch connected wallets using React Query
  // const { data: connectedWallets, isLoading: isWalletsLoading } = useQuery({
  //   queryKey: ["user-wallets"],
  //   queryFn: async () => {
  //     const response = await apiClient.getWallets();
  //     if (isGetWalletResponse(response)) {
  //       // Map API wallet objects to your AddedWallet interface
  //       return response.wallets.map((w: any, idx: number) => ({
  //         id: idx + 1,
  //         address: w.address,
  //         networkName: w.chain || "ETH",
  //         thinkAmount: 0, // Placeholder, update if you have logic for this
  //         imageUrl: w.chain === "ROOT" ? "/images/logos/root-network-icon.png" : "/images/logos/eth-icon.png",
  //         primary: w.primary,
  //       }));
  //     }
  //     return [];
  //   },
  //   refetchOnWindowFocus: "always",
  // });

  const claimables = claimableResponse?.claimables;

  if ((typeof totalClaimableTokens !== "number") || !claimables) {
    return null;
  }

  const thinkBalance = thinkTokenDetails?.balance || 0;

  let claimedView = null;
  if (totalClaimedTokens && totalClaimedTokens > 0) {
    claimedView = (
      <div style={{ background: 'radial-gradient(350.4% 184.96% at 50% 50.52%,rgb(203, 164, 119) 0%,rgb(146, 94, 37) 100%)' }} className="rounded-t-[32px] border-t-2 border-r-2 border-b-2 border-l-2 border-neutral-800">
        <div className="flex flex-col items-center ">
          <p className="text-base text-white p-9 pb-3 uppercase font-mono">You have claimed $THINK</p>
          <h2 className="text-7xl p-9 pt-3 font-[evolver-variable]">
            {/* Placeholder for the large number, update if it should be dynamic, e.g. from claimable amount */}
            {formatNumberWithCommas(totalClaimedTokens.toFixed(2))}
          </h2>
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <div className="my-8 bg-black text-white shadow-lg rounded-b-[32px] max-w-[640px] mx-auto">
        {claimedView ? claimedView : (
          <div style={{ background: 'radial-gradient(350.4% 184.96% at 50% 50.52%, #21D0FC 0%, #0E0F12 100%)' }} className="rounded-t-[32px] border-t-2 border-r-2 border-b-2 border-l-2 border-neutral-800">
            <div className="flex flex-col items-center ">
              <p className="text-base text-white p-9 pb-3 uppercase font-mono">total $think claimable</p>
              <h2 className="text-7xl p-9 pt-3 font-[evolver-variable]">
                {/* Placeholder for the large number, update if it should be dynamic, e.g. from claimable amount */}
                {formatNumberWithCommas(totalClaimableTokens.toFixed(2))}
              </h2>
            </div>
          </div>
        )}

        <div className="p-6 border-r-2 border-l-2 border-b-2 border-neutral-800 rounded-b-[32px]">
          {(claimableQuery && claimableQuery.isLoading) && <div className="text-center my-4">Loading claiming balances...</div>}
          {/* {(astoTokenDetailsQuery.isError || thinkTokenDetailsQuery.isError) && <div className="text-center my-4 text-red-500">Error loading token data. Please refresh.</div>} */}

          <div className="mt-6 rounded-lg">
            {/* Table Header */}
            <div className="grid grid-cols-6 gap-x-4 py-3 font-mono text-black-400 rounded-t-lg">
              <div className="text-sm col-span-1 ">QTY</div>
              <div className="text-sm col-span-3">ONCHAIN PARTICIPATION</div>
              <div className="text-sm col-span-2 text-right">$THINK AMOUNT</div>
            </div>
            {claimables ? (
              <NftAccordion
                walletAddress={walletAddress}
                title={"THINK Agent Bundle"}
                tooltip={<div>
                  Claim your 2x multiplier by XX/XX/XX.
                  <br />
                  Read <a href="https://docs.thinkagents.ai" target="_blank" rel="noopener noreferrer" className="underline text-[#22D0FB] hover:text-[#22D0FB]">FAQs</a> for more information.
                </div>}
                claimables={claimables.filter(c => c.collectionName === "think-agent-bundle")}
                collectionAddress={"******************************************".toLowerCase()}
                isInitiallyExpanded={true}
              />
            ) : null}

            {claimables ? (
              <NftAccordion
                walletAddress={walletAddress}
                title={"6079 + ALPHi Founders"}
                imageUrl='/images/6079.png'
                claimables={claimables.filter(c => c.collectionName === "6079")}
                isInitiallyExpanded={true}
              />
            ) : null}

            {claimables ? (
              <NftAccordion
                walletAddress={walletAddress}
                title={"BASIIC"}
                imageUrl='/images/basiic.png'
                claimables={claimables.filter(c => c.collectionName === "basiic")}
                isInitiallyExpanded={true}
              />
            ) : null}
          </div>

          <AstoTokenProvider>
            <AstoConversionCard />
          </AstoTokenProvider>

          {/* TODO provide correct action based on state information. */}
          <div className="mt-8">
            {totalClaimableTokens > 0 && (
              <button
                type="button"
                onClick={() => openClaimModal(true)}
                className="
                  inline-flex items-center justify-center gap-2 w-full whitespace-nowrap text-lg text-black font-medium transition-colors h-9 px-4 py-2 !h-[55px] !px-2 rounded-full text-center
                  bg-gradient-to-b from-[#21D0FC] from-[23.44%] to-[#ECDCAA] to-[77.6%]
                  hover:bg-[#21D0FC] duration-300
                "
              >
                Claim and stake $THINK
              </button>
            )}
            {totalClaimableTokens === 0 && new Big(thinkBalance).gt(new Big(0)) && (
              <button
                type="button"
                onClick={() => setIsStakeModalOpen(true)}
                className="
                  inline-flex items-center justify-center gap-2 w-full whitespace-nowrap text-lg text-black font-medium transition-colors h-9 px-4 py-2 !h-[55px] !px-2 rounded-full text-center
                  bg-gradient-to-b from-[#21D0FC] from-[23.44%] to-[#ECDCAA] to-[77.6%]
                  hover:bg-[#21D0FC] duration-300
                "
              >
                Stake $THINK
              </button>
            )}
            {totalClaimableTokens === 0 && thinkBalance === 0 && (
              <button
                type="button"
                onClick={() => alert("Go to Dashboard functionality to be implemented!")}
                className="
                  inline-flex items-center justify-center gap-2 w-full whitespace-nowrap text-lg text-black font-medium transition-colors h-9 px-4 py-2 !h-[55px] !px-2 rounded-full text-center
                  bg-gradient-to-b from-[#21D0FC] from-[23.44%] to-[#ECDCAA] to-[77.6%]
                  hover:bg-[#21D0FC] duration-300
                "
              >
                Go to Dashboard
              </button>
            )}
          </div>
        </div>
      </div>
      <ThinkDialog
        open={isClaimModalOpen}
        onOpenChange={(isOpen) => { setIsClaimModalOpen(isOpen); }}
        {...ClaimStakeDialogContent({
          ...claimDetails,
          onClose: () => { setIsClaimModalOpen(false); }
        })}
      />
      <ThinkDialog
        open={isStakeModalOpen}
        onOpenChange={(isOpen) => { setIsStakeModalOpen(isOpen); }}
        {...StakeDialogContent({
          ...stakeDetails,
          onClose: () => { setIsStakeModalOpen(false); }
        })}
      />
      <ThinkDialog
        open={goldenTicketOpen}
        isGoldenTicket={true}
        onOpenChange={(isOpen) => { setGoldenTicketOpen(isOpen); }}
        {...GoldenTicketDialogContent({
          onClose: () => { setGoldenTicketOpen(false); },
          goldenTicketDetails: {
            totalGoldenTicketThink: goldenTicketAmount.toFixed(2)
          },
        })}
      />
    </TooltipProvider>
  );
};

export default ThinkTokenCard;
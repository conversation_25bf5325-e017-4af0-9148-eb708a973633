import React from 'react';
import { truncateAddress, calculateRootAstoThink } from './TokenFunctions';
import Big from 'big.js';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/components/auth/AuthContext';
import env from "@/environments/config";
import { PORCINI_SWAPPER_CONTRACT_ADDRESS } from "@/lib/swap/asto";
import {
  FutureverseAuthProvider,
  FutureverseWagmiProvider,
  useAuth as useFutureverseAuth,
} from '@futureverse/auth-react';
import { FutureverseAuthClient } from '@futureverse/auth-react/auth';
import { AuthUiProvider, type ThemeConfig, DefaultTheme } from '@futureverse/auth-ui';
import { useFutureverseSigner } from '@futureverse/auth-react';
import { rootSwapperABI } from '@/lib/swap/abi-root-swapper';
import { ethers } from 'ethers';
import { createWagmiConfig } from '@futureverse/auth-react/wagmi';
import { QueryClient, QueryClientProvider as TanstackQueryClientProvider } from '@tanstack/react-query';
import { mainnet as viemMainnet, sepolia as viemSepolia, polygonAmoy as viemPolygonAmoy } from 'viem/chains';
import { rootNetwork, porciniTestnet } from "@/lib/web3Config";
import ThinkDialog, { AstoConversionStages } from '../think-provider/ThinkDialog';
import { AstoConverterDialogContent } from '../think-provider/dialogs/AstoConversion';
import { useBalance } from 'wagmi';

// Futureverse Specific Configuration
const fvClientId = env.FV_CLIENT_ID || '<your-futureverse-client-id>';
const fvWalletConnectProjectId = env.FV_WALLET_CONNECT_PROJECT_ID || '<your-wallet-connect-project-id>';
const fvXamanAPIKey = env.FV_XAMAN_API_KEY || '<your-xaman-application-key>';
const fvAuthRedirectUri = env.FV_AUTH_REDIRECT_URI || '<your-auth-redirect-uri>';

export const fvAuthClient = new FutureverseAuthClient({
  clientId: fvClientId,
  environment: env.FV_ENVIRONMENT as 'production' | 'staging' | 'development',
  redirectUri: fvAuthRedirectUri,
  signInFlow: 'redirect',
});

const fvQueryClient = new QueryClient();

export const getFvWagmiConfig = async () => {
  return createWagmiConfig({
    walletConnectProjectId: fvWalletConnectProjectId,
    xamanAPIKey: fvXamanAPIKey,
    authClient: fvAuthClient,
    ssr: false,
    chains: [viemMainnet, viemSepolia, viemPolygonAmoy, rootNetwork, porciniTestnet] as any,
  });
};

const customFvThemeConfig: ThemeConfig = {
  ...DefaultTheme,
  defaultAuthOption: 'web3',
  images: {
    logo: '/images/logos/think-logo-primary.svg',
  },
};

// Minimal ERC20 ABI for approve function
const erc20ApproveABI = [
  {
    "inputs": [
      {
        "internalType": "address",
        "name": "spender",
        "type": "address"
      },
      {
        "internalType": "uint256",
        "name": "amount",
        "type": "uint256"
      }
    ],
    "name": "approve",
    "outputs": [
      {
        "internalType": "bool",
        "name": "",
        "type": "bool"
      }
    ],
    "stateMutability": "nonpayable",
    "type": "function"
  }
];

// ASTO token contract address on Porcini
const PORCINI_ASTO_CONTRACT_ADDRESS = "******************************************";

const RootNetworkAstoBurnCore: React.FC = () => {
  const { user } = useAuth();
  const walletAddress = user?.address;

  const { userSession: fvUserSession,
    signIn: fvSignIn,
    signOutPass: fvSignOut,
    isFetchingSession: isFvAuthLoading
  } = useFutureverseAuth();
  const fvUser = fvUserSession?.user;
  const fvSigner = useFutureverseSigner();

  // Modal state management - consistent with AstoThinkConverter
  const [modalStage, setModalStage] = React.useState<AstoConversionStages>('initial');
  const modalStageRef = React.useRef(modalStage);
  React.useEffect(() => {
    modalStageRef.current = modalStage;
  }, [modalStage]);

  const [approvalTxHash, setApprovalTxHash] = React.useState<string | null>(null);
  const [burnTxHash, setBurnTxHash] = React.useState<string | null>(null);
  const [currentError, setCurrentError] = React.useState<string | null>(null);
  const [isBurnModalOpen, setBurnModalOpen] = React.useState(false);

  // Processing state for the modal
  const [isProcessing, setIsProcessing] = React.useState(false);

  // Root Network ASTO balance query using wagmi useBalance hook
  const chainId = env.FV_ENVIRONMENT === 'development' ? porciniTestnet.id : rootNetwork.id;

  console.log("fvUser address", fvUser);

  const {
    data: balanceData,
    isLoading: isBalanceLoading,
    error: balanceError,
    refetch: refetchBalance
  } = useBalance({
    address: fvUser?.profile?.eoa as `0x${string}`,
    token: PORCINI_ASTO_CONTRACT_ADDRESS as `0x${string}`,
    chainId: chainId,
    query: {
      enabled: !!fvUser?.profile?.eoa,
      retry: 1,
      refetchOnWindowFocus: "always",
    }
  });

  console.log("balanceData", balanceData);

  // Transform wagmi balance data to match our expected format
  const rootAstoData = React.useMemo(() => {
    if (balanceError) {
      console.error("Error fetching ASTO balance with wagmi:", balanceError);
      return {
        balance: 0,
        decimals: 18,
        symbol: "ASTO",
        value: BigInt(0),
        formatted: "0"
      };
    }

    if (balanceData) {
      return {
        balance: parseFloat(balanceData.formatted),
        decimals: balanceData.decimals,
        symbol: balanceData.symbol,
        value: balanceData.value,
        formatted: balanceData.formatted
      };
    }

    return null;
  }, [balanceData, balanceError]);

  const rootAstoAmountToBurn = rootAstoData && rootAstoData.balance ? rootAstoData.balance.toString() : "0";

  // State for Root Network swappable THINK (async calculation)
  const [rootSwappableThink, setRootSwappableThink] = React.useState<string>("0");

  // Effect to calculate Root Network swappable THINK
  React.useEffect(() => {
    if (!fvUser) return;
    const calculateRootSwappable = async () => {
      if (rootAstoData && rootAstoData.balance > 0) {
        try {
          const result = await calculateRootAstoThink(rootAstoData.balance);
          setRootSwappableThink(result);
        } catch (error) {
          console.error("Error calculating Root Network swappable THINK:", error);
          setRootSwappableThink("0");
        }
      } else {
        setRootSwappableThink("0");
      }
    };

    calculateRootSwappable();
  }, [rootAstoData?.balance, fvUser]);

  const truncatedFvAddress = fvUser?.profile?.sub ? truncateAddress(fvUser.profile.sub) : "Not Connected";

  // Handle FuturePass login
  const handleFuturePassLogin = async () => {
    console.log("FuturePass login initiated");

    if (fvSignIn && walletAddress) {
      try {
        await fvSignIn({
          type: 'eoa',
          signer: fvSigner,
          address: walletAddress,
        });
      } catch (error) {
        console.error("Failed to sign in with Futureverse:", error);
        setCurrentError("Failed to connect with Futureverse. Please try again.");
        setModalStage('error');
      }
    } else {
      setCurrentError("Please connect your wallet first to use Futureverse authentication.");
      setModalStage('error');
    }
  };

  const getRootNetworkExplorerUrl = () => {
    if (env.FV_ENVIRONMENT === 'development') {
      return "https://porcini.rootscan.io";
    } else {
      return "https://explorer.rootnet.live";
    }
  }

  // Open modal function
  const openBurnModal = () => {
    if (!fvUser) {
      handleFuturePassLogin();
      return;
    }

    setModalStage('initial');
    setApprovalTxHash(null);
    setBurnTxHash(null);
    setCurrentError(null);
    setBurnModalOpen(true);
  };

  // Handle modal cancel
  const handleModalCancel = () => {
    setBurnModalOpen(false);
    if (modalStageRef.current !== 'success' && modalStageRef.current !== 'error') {
      setModalStage('initial');
    }
    setIsProcessing(false);
  };

  // Handle approval (Step 1)
  const handleApprove = async () => {
    if (!fvUser || !fvSigner || parseFloat(rootAstoAmountToBurn) <= 0) {
      setCurrentError("Cannot proceed: Missing user data, signer, or invalid amount.");
      setModalStage('error');
      return;
    }

    setModalStage('approving');
    setCurrentError(null);
    setIsProcessing(true);

    try {
      const amountInWei = new Big(rootAstoAmountToBurn).times(new Big(10).pow(18)).toFixed(0);
      const swapperContractAddress = env.FV_ENVIRONMENT === 'development'
        ? PORCINI_SWAPPER_CONTRACT_ADDRESS
        : PORCINI_SWAPPER_CONTRACT_ADDRESS;

      const approveIface = new ethers.Interface(erc20ApproveABI);
      const approveData = approveIface.encodeFunctionData('approve', [swapperContractAddress, amountInWei]);

      const approveTransaction = {
        to: PORCINI_ASTO_CONTRACT_ADDRESS,
        data: approveData,
        value: '0x0',
      };

      let approveTx;

      if ((fvSigner as any)._walletClient) {
        const fvSignerWalletClient = (fvSigner as any)._walletClient;
        approveTx = await fvSignerWalletClient.sendTransaction(approveTransaction);
      } else if ('sendTransaction' in fvSigner && typeof (fvSigner as any).sendTransaction === 'function') {
        approveTx = await (fvSigner as any).sendTransaction(approveTransaction);
      } else {
        throw new Error("No suitable transaction method available");
      }

      setApprovalTxHash(approveTx.hash);

      // Wait for approval confirmation
      await approveTx.wait();
      setModalStage('approved');
      setCurrentError(null);
      setIsProcessing(false);

    } catch (err: any) {
      console.error("Approval error:", err);
      setCurrentError(err.message || "Failed to approve tokens.");
      setModalStage('error');
      setIsProcessing(false);
    }
  };

  // Handle burn/swap (Step 2)
  const handleConfirmBurn = async () => {
    if (!fvUser || !fvSigner || parseFloat(rootAstoAmountToBurn) <= 0) {
      setCurrentError("Cannot proceed: Missing required data or invalid amount.");
      setModalStage('error');
      return;
    }

    setModalStage('burning');
    setCurrentError(null);
    setIsProcessing(true);

    try {
      const amountInWei = new Big(rootAstoAmountToBurn).times(new Big(10).pow(18)).toFixed(0);
      const swapperContractAddress = env.FV_ENVIRONMENT === 'development'
        ? PORCINI_SWAPPER_CONTRACT_ADDRESS
        : PORCINI_SWAPPER_CONTRACT_ADDRESS;

      const swapIface = new ethers.Interface(rootSwapperABI);
      const swapData = swapIface.encodeFunctionData('swap', [amountInWei]);

      const swapTransaction = {
        to: swapperContractAddress,
        data: swapData,
        value: '0x0',
      };

      let swapTx;

      if ((fvSigner as any)._walletClient) {
        const fvSignerWalletClient = (fvSigner as any)._walletClient;
        swapTx = await fvSignerWalletClient.sendTransaction(swapTransaction);
      } else if ('sendTransaction' in fvSigner && typeof (fvSigner as any).sendTransaction === 'function') {
        swapTx = await (fvSigner as any).sendTransaction(swapTransaction);
      } else {
        throw new Error("No suitable transaction method available");
      }

      setBurnTxHash(swapTx.hash);

      // Wait for swap confirmation
      await swapTx.wait();
      setModalStage('success');
      setCurrentError(null);
      setIsProcessing(false);
      refetchBalance(); // Refetch balances

    } catch (err: any) {
      console.error("Swap error:", err);
      setCurrentError(err.message || "Failed to swap tokens.");
      setModalStage('error');
      setIsProcessing(false);
    }
  };

  return (
    <>
      {/* ASTO root network Balance */}
      <div className="lg:grid lg:grid-cols-3 items-center gap-x-4 mt-8">
        <div className="flex items-center col-span-1">
          <img src="/images/logos/root-network-icon.png" alt="ASTO" className="w-9 h-9 mr-2.5 rounded-full bg-purple-900 p-1" />
          <div>
            <p className="font-semibold text-white text-sm font-mono">
              $ASTO ({env.FV_ENVIRONMENT === 'development' ? 'Porcini' : 'Root'})
            </p>
            <p className="text-xs text-purple-300 font-mono">{fvUser ? truncatedFvAddress : "Connect FuturePass"}</p>
            {env.FV_ENVIRONMENT === 'development' && (
              <p className="text-xs text-yellow-400 font-mono">Testnet Mode</p>
            )}
          </div>
        </div>
        <div className="ml-11 lg:m1-0 col-span-1 text-left">
          {fvUser && (
            <>
              <p className="font-semibold text-white text-sm">
                {isBalanceLoading ? "Loading..." : `${rootAstoAmountToBurn} ASTO`}
              </p>
              <p className="text-xs text-purple-300">
                {isBalanceLoading ? "..." : rootSwappableThink} $THINK
              </p>
            </>
          )}
          {!fvUser && (
            <p className="text-xs text-purple-300"></p>
          )}
        </div>
        <div className="col-span-1 text-right flex justify-end">
          {fvUser && (
            <button
              onClick={() => fvSignOut()}
              disabled={isFvAuthLoading}
              className="text-xs text-red-400 hover:text-red-300 border border-red-400 hover:border-red-300 px-2 py-1 rounded transition-colors duration-150 disabled:opacity-50 mr-2"
              title="Sign out from FuturePass"
            >
              Sign Out
            </button>
          )}
          <button
            onClick={fvUser ? openBurnModal : handleFuturePassLogin}
            disabled={isFvAuthLoading || isBalanceLoading || isProcessing}
            className="bg-[#854BD9] hover:bg-[#2D1058] text-white uppercase font-semibold py-1 px-2 rounded-md text-sm transition-colors duration-150 shadow-md hover:shadow-lg disabled:opacity-50 flex items-center gap-1"
          >
            {!isFvAuthLoading && !isProcessing && (
              <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                <path d="M12.3998 9.30003C12.3998 11.8527 10.8289 12.9729 9.61507 13.3506C9.35623 13.4312 9.18611 13.1294 9.34095 12.9069C9.86919 12.1478 10.4798 10.9895 10.4798 9.90003C10.4798 8.72967 9.49336 7.34793 8.72308 6.49569C8.54697 6.30085 8.2398 6.42977 8.2302 6.69223C8.19838 7.56175 8.06117 8.72509 7.4695 9.63684C7.37426 9.78359 7.1721 9.79576 7.06359 9.65853C6.87872 9.42473 6.69385 9.13591 6.50899 8.90775C6.40941 8.78485 6.22943 8.78316 6.11457 8.89191C5.66676 9.31594 5.0398 9.97716 5.0398 10.8C5.0398 11.3936 5.32268 12.1324 5.61427 12.7045C5.74669 12.9643 5.50103 13.269 5.24866 13.1229C4.14715 12.4853 2.7998 11.1642 2.7998 9.30003C2.7998 7.41216 5.38598 4.79692 6.37344 2.32617C6.52925 1.9363 7.00946 1.7531 7.34345 2.00752C9.36617 3.54836 12.3998 6.5269 12.3998 9.30003Z" stroke="#F8F8F8" strokeWidth="1.2" />
              </svg>
            )}
            {isFvAuthLoading ? "Loading..." :
              isProcessing ? "Processing..." :
                fvUser ? `Burn (${env.FV_ENVIRONMENT === 'development' ? 'Porcini' : 'Root'})` :
                  "Connect with Root"}
          </button>
        </div>
      </div>

      <ThinkDialog
        open={isBurnModalOpen}
        onOpenChange={(isOpen) => { if (!isOpen) handleModalCancel(); else setBurnModalOpen(true); }}
        {...AstoConverterDialogContent({
          modalStage,
          isProcessing: isProcessing || modalStage === 'approving' || modalStage === 'burning',
          astoBurnDetails: {
            astoBalanceToBurn: rootAstoAmountToBurn,
            calculatedThinkToReceive: rootSwappableThink,
            approvalTxHash,
            burnTxHash,
            explorerUrl: getRootNetworkExplorerUrl(),
            errorMessage: currentError,
            onApprove: handleApprove,
            onConfirmBurn: handleConfirmBurn,
            onCancel: handleModalCancel,
          },
        })}
      />
    </>
  );
};

// Wrapper component to provide Futureverse context
const RootNetworkAstoBurn: React.FC = () => {
  return (
    <TanstackQueryClientProvider client={fvQueryClient}>
      <FutureverseWagmiProvider getWagmiConfig={getFvWagmiConfig as any}>
        <FutureverseAuthProvider authClient={fvAuthClient as any}>
          <AuthUiProvider authClient={fvAuthClient as any} themeConfig={customFvThemeConfig}>
            <RootNetworkAstoBurnCore />
          </AuthUiProvider>
        </FutureverseAuthProvider>
      </FutureverseWagmiProvider>
    </TanstackQueryClientProvider>
  );
};

export default RootNetworkAstoBurn;

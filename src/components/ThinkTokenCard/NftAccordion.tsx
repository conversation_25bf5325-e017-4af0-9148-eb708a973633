import React, { useState, useEffect, ReactNode } from 'react';
import { web3Client, isGetNFTResponse } from '@/lib/api'; // Added api imports
import { EligibleSnapshot, NetworkNfts, NFTData } from '@/types'; // Added types import
import { IconChevronDown } from '../ui/IconChevronDown';
import { calculateAmountFromDecimal, formatNumberWithCommas } from './TokenFunctions';
import { Tooltip, TooltipContent, TooltipTrigger } from '../ui/tooltip';
import { IconIndicator } from '../ui/IconIndicator';
import { useClaim } from '../think-provider/ClaimProvider';
import Big from 'big.js';

export interface DisplayNftItem {
  id: string;
  name: string;
  collectionName?: string;
  imageUrl?: string;
  contractAddress?: string;
  thinkAmount: bigint;
  decimals: bigint;
  claimableId?: bigint;
  isGoldenTicket: boolean;
  status: string;
}

interface NftAccordionProps {
  walletAddress?: `0x${string}` | string; // Wallet address to fetch NFTs for
  isInitiallyExpanded?: boolean;
  collectionAddress?: string;
  imageUrl?: string;
  title: string;
  claimables: EligibleSnapshot[];
  tooltip?: ReactNode;
  // isLoading, items, and totalThinkForBundle are now managed internally
  // Add a callback if ThinkTokenCard needs the loaded items or totalThink
  // onNftsLoaded?: (items: DisplayNftItem[], totalThink: number) => void;
}

export const NftAccordion: React.FC<NftAccordionProps> = ({
  walletAddress,
  isInitiallyExpanded = true,
  collectionAddress,
  imageUrl,
  title,
  claimables,
  tooltip,
  // onNftsLoaded,
}) => {
  const [isExpanded, setIsExpanded] = useState(isInitiallyExpanded);
  const [isLoadingNfts, setIsLoadingNfts] = useState(true);
  const [displayedNftItems, setDisplayedNftItems] = useState<DisplayNftItem[]>([]);
  const [totalThinkForBundle, setTotalThinkForBundle] = useState("");
  const [nfts, setNfts] = useState<NetworkNfts[]>([]);
  const [currentNFTContext, setCurrentNFTContext] = useState("");

  // Moved from ThinkTokenCard
  const targetNetwork = "eth-mainnet"; // TODO: Confirm this network identifier

  const assignTotalFromData = (displayed: DisplayNftItem[]) => {
    const decimals = displayed.find(item => item.decimals)?.decimals || BigInt(18);
    const total = displayed.reduce((sum , item) => sum + (item.thinkAmount > 0 ? item.thinkAmount : BigInt(0)), BigInt(0));
    const totalString = calculateAmountFromDecimal(total, decimals, 2);
    setTotalThinkForBundle(formatNumberWithCommas(totalString));
  }

  const updatedViewsFromNfts = async (targetContractAddress: string, updatedNfts: NetworkNfts[] = nfts) => {
    const filteredNfts: DisplayNftItem[] = [];

    updatedNfts.forEach(networkNfts => {
      if (networkNfts.network.toLowerCase() === targetNetwork) {
        networkNfts.nfts.ownedNfts.forEach((nft: NFTData) => {
          if (nft.contract.address.toLowerCase() === targetContractAddress) {
            const claimable = claimables.find(c => {
              // console.log("nft", c.collectionName, c.purpose);
              // console.log("claimable", nft.collection.slug, nft.tokenId);
              // console.log("found?", c.purpose === nft.tokenId && c.collectionName === nft.collection.slug)
              return c.purpose === nft.tokenId && c.collectionName === nft.collection.slug
            });
            const goldenTicket = claimables.find(c => {
              return c.purpose === `golden_ticket_${nft.tokenId}` && c.collectionName === nft.collection.slug
            });

            const NftData = {
              id: nft.tokenId || `${nft.contract.address}-${nft.name?.replace(/\s+/g, '-') || 'nft'}`,
              name: nft.name || 'Unnamed NFT',
              collectionName: nft.contract.name || nft.collection?.name,
              imageUrl: nft.image?.pngUrl || nft.image?.originalUrl,
              contractAddress: nft.contract.address
            };

            let claimData : {
              thinkAmount: bigint;
              decimals: bigint;
              claimableId?: bigint;
              status: string;
              isGoldenTicket: boolean;
            } = {
                thinkAmount: BigInt("000000000000000000"),
                decimals: BigInt(18),
                claimableId: undefined,
                status: "Unknown",
                isGoldenTicket: false
            }
            if (claimable) {
              claimData = {
                thinkAmount: BigInt(claimable.tokenAmount),
                decimals: BigInt(claimable.tokenDecimals),
                claimableId: BigInt(claimable.id),
                status: claimable.claimStatus || "Unknown",
                isGoldenTicket: goldenTicket ? true : false,
              }
            }
            filteredNfts.push({
              ...NftData,
              ...claimData,
            });
          }
        });
      }
    });
    setDisplayedNftItems(filteredNfts);
    assignTotalFromData(filteredNfts);
    // if (onNftsLoaded) { // Inform parent if callback exists
    //   const currentTotalThink = filteredNfts.reduce((sum, item) => sum + (item.thinkAmount || 0), 0);
    //   onNftsLoaded(filteredNfts, currentTotalThink);
    // }
  };

  useEffect(() => {
    if (!walletAddress || !collectionAddress) {
      setIsLoadingNfts(false);
      setDisplayedNftItems([]); // Clear items if no wallet address
      // if (onNftsLoaded) onNftsLoaded([], 0); // Inform parent if callback exists
      return;
    }
    // TODO cache NFTS and only refresh NFTs when address/network/collection changes

    const fetchNewNfts = async () => {
      setIsLoadingNfts(true);
      try {
        const walletNftsResponse = await web3Client.getWalletNFTs(walletAddress);
        if (isGetNFTResponse(walletNftsResponse)) {
          setNfts(walletNftsResponse.allNfts);
          updatedViewsFromNfts(collectionAddress, walletNftsResponse.allNfts);
        } else {
          console.error("Failed to fetch or parse NFT response in Accordion:", walletNftsResponse);
          setDisplayedNftItems([]);
          assignTotalFromData([]);
          // if (onNftsLoaded) onNftsLoaded([], 0);
        }
        try {
        } catch (error) {
          console.error("Error fetching NFTs in Accordion:", error);
          setDisplayedNftItems([]);
          assignTotalFromData([]);
          // if (onNftsLoaded) onNftsLoaded([], 0);
        } finally {
          setIsLoadingNfts(false);
        }
      } catch (error) {
        console.error("Error fetching NFTs in Accordion:", error);
        setDisplayedNftItems([]);
          assignTotalFromData([]);
        // if (onNftsLoaded) onNftsLoaded([], 0);
      } finally {
        setIsLoadingNfts(false);
      }
    };

    fetchNewNfts();
  }, [currentNFTContext]);

  useEffect(() => {
    const newContext = `${walletAddress}-${targetNetwork}-${collectionAddress}`;
    if (collectionAddress && newContext !== currentNFTContext) {
      setCurrentNFTContext(newContext);
    } else if (collectionAddress) {
      updatedViewsFromNfts(collectionAddress);
    } else {
      const displayables = claimables.map(c => ({
        id: c.purpose || "",
        name: c.purpose || "",
        thinkAmount: BigInt(c.tokenAmount),
        imageUrl: imageUrl,
        decimals: BigInt(c.tokenDecimals),
        claimableId: BigInt(c.id),
        status: c.claimStatus || "Unknown",
        isGoldenTicket: false,
      }))
      setDisplayedNftItems(displayables);
      assignTotalFromData(displayables);
      setIsLoadingNfts(false);
    }
  }, [claimables, walletAddress, collectionAddress, targetNetwork /*, onNftsLoaded */]);

  return (
    <div>

      {/* Summary Row (Clickable) */}
      <div
        onClick={() => setIsExpanded(!isExpanded)}
        className="border-t border-neutral-700 bg-neutral-900/50 border-dashed grid grid-cols-6 gap-x-4 py-4 items-center cursor-pointer hover:bg-neutral-700/20 transition-colors duration-150 font-token"
      >
        <div className="col-span-1 text-lg">
          {displayedNftItems.length} {/* Use internal state */}
        </div>
        <div className="col-span-3">
          {title}
          {tooltip && (
              
            <Tooltip delayDuration={0}>
              <TooltipTrigger onClick={(e) => e.preventDefault()}>
                <span className="pl-2 inline-block"><IconIndicator /></span>
              </TooltipTrigger>
              <TooltipContent
                  side="top"
                  align="center"
              >
                <div className="flex items-center flex-col justify-end -mb-2">
                  <div className="transform origin-bottom-right bg-neutral-700 border-2 border-neutral-700 text-base text-white p-2 rounded-lg max-w-[400px] flex-1">
                    {tooltip}
                  </div>
                  <div className="ml-3 w-5 overflow-hidden">
                    <div className="h-3 bg-neutral-700 -rotate-45 transform origin-top-left rounded-sm"></div>
                  </div>
                </div>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
        <div className="col-span-2 text-right font-semibold">{totalThinkForBundle}{/* Uses internal calculation */}
          <span className={`ml-2`}>
            <IconChevronDown className={`${isExpanded ? 'rotate-0' : '-rotate-90'} transition-transform duration-200 w-6 text-off-white inline`} />
          </span>
        </div>
      </div>

      {/* Accordion Content (Detailed Items) */}
      {isExpanded && (
        <div className="max-h-[235px] overflow-y-scroll scrollbar-style">
          {isLoadingNfts ? ( /* Use internal state */
            <div className="px-4 py-3 text-center">Loading NFTs...</div>
          ) : displayedNftItems.length === 0 ?  /* Use internal state */
            collectionAddress ? (
              <div className="px-4 py-3 text-center">No NFTs found for this contract on this wallet.</div>
            ) : (
              <div className="px-4 py-3 text-center">No claimables found for this collection.</div>
            )
           : (
            displayedNftItems.map((item) => ( /* Use internal state */
              <div
                key={item.id}
                className={`grid grid-cols-4 gap-x-4 py-1 last:pb-3 font-mono items-center hover:bg-neutral-700/30 transition-colors duration-150`}
              >
                <div className="col-span-2 min-w-[200px]">
                  <div className={`flex items-center`}>
                    {item.imageUrl && <div className="border-2 rounded-md border-neutral-800 overflow-hidden mr-3"><img src={item.imageUrl} alt={item.name} className="w-10 h-10 rounded-md object-cover bg-neutral-700" /></div>}
                    <span className={`text-md ${item.isGoldenTicket ? "bg-gradient-to-b from-[#F7B359] to-[#FFF2CF] bg-clip-text text-transparent" : ""}`}> {collectionAddress ? `#${item.id || 'N/A'}` : item.name}</span>
                    {item.isGoldenTicket && (
                      <Tooltip delayDuration={0}>
                        <TooltipTrigger onClick={(e) => e.preventDefault()}>
                          <img src="/images/golden_ticket.png" alt={item.name} className="h-6 ml-3" />
                        </TooltipTrigger>
                        <TooltipContent
                            side="top"
                            align="center"
                        >
                          <div className="flex items-center flex-col justify-end -mb-2">
                            <div className="transform origin-bottom-right bg-neutral-700 border-2 border-neutral-700 text-base text-white p-2 rounded-lg max-w-[400px] flex-1">
                              Airdrop will be provided after <br /> 90-day reward cycle.
                            </div>
                            <div className="ml-3 w-5 overflow-hidden">
                              <div className="h-3 bg-neutral-700 -rotate-45 transform origin-top-left rounded-sm"></div>
                            </div>
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    )}
                  </div>
                </div>
                <div className={` col-span-2 text-right text-md`}>{new Big(item.thinkAmount.toString()).gt(new Big(0)) ? formatNumberWithCommas(calculateAmountFromDecimal(item.thinkAmount, item.decimals, 2)) : "0.00"}</div> {/* Placeholder for $THINK Amount */}
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
};
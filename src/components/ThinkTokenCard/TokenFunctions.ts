import Big from 'big.js';
import { getRootSwapperDetails } from '@/lib/swap/asto';
import env from "@/environments/config";
import { sepolia } from "@account-kit/infra";

export const truncateAddress = (address: string): string => {
  if (!address) return "";
  return `${address.substring(0, 6)}...${address.substring(address.length - 4)}`;
};

export const getExplorerUrl = () => {
  if (env.PRIMARY_CHAIN === sepolia) {
    return "https://sepolia.etherscan.io"; // Sepolia
  } else {
    return "https://etherscan.io"; // Mainnet
  }
}

export function calculateAstoThink(astoAmount: string, astoTotalSupply: string, thinkTotalSupply: string, decimals: number = 18, precision?: number): string {
  // Check for zero or empty values to prevent division by zero
  if (!astoAmount || !astoTotalSupply || !thinkTotalSupply) {
    return "0";
  }

  try {
    const bigAstoAmount = new Big(astoAmount);
    const bigAsto = new Big(astoTotalSupply);
    const bigThink = new Big(thinkTotalSupply);

    // Check if any of the values are zero
    if (bigAstoAmount.eq(0) || bigAsto.eq(0) || bigThink.eq(0)) {
      return "0";
    }

    const numerator = bigAstoAmount.mul(bigThink.mul(0.69));
    const denominator = bigAsto.prec(decimals);

    // Additional check to ensure denominator is not zero
    if (denominator.eq(0)) {
      return "0";
    }

    const thinkAmount = numerator.div(denominator).prec(decimals);
    return precision ? thinkAmount.toFixed(precision) : thinkAmount.toFixed(decimals);
  } catch (error) {
    console.error("Error in calculateAstoThink:", error);
    return "0";
  }
}

export function calculateAmountFromDecimal(amount: bigint, decimals: bigint, precision?: number): string {
  const changePrecision = precision ? precision : Number(decimals);
  const total = new Big(amount.toString()).div(new Big(10).pow(Number(decimals)))
  const totalWithPrecision = total.toFixed(changePrecision);
  return totalWithPrecision
}

export function formatNumberWithCommas(numberString: string): string {
  return numberString.replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export async function calculateRootAstoThink(astoAmount: number): Promise<string> {
  try {
    // Get real contract values from Porcini swapper
    const swapperData = await getRootSwapperDetails();

    // Parse values from contract
    const totalAstoBig = new Big(swapperData.totalAsto);
    const totalThinkBig = new Big(swapperData.totalThink);
    // const rateBig = new Big(swapperData.rate);
    // const rateScaledBig = new Big(swapperData.scaledRate);
    // const scaleBig = new Big(swapperData.scale);

    // Check if contract is paused
    if (swapperData.paused) {
      console.warn("Contract is paused");
      return "0";
    }

    return calculateAstoThink(`${astoAmount}`, totalAstoBig.toString(), totalThinkBig.toString())

  } catch (error) {
    console.error('Error in Root Network ASTO->THINK calculation:', error);
    // Fallback to simple rate calculation if contract read fails
    const fallbackRate = 0.69;
    const result = astoAmount * fallbackRate;
    console.log(`Using fallback rate: ${astoAmount} ASTO = ${result} THINK`);
    return result.toString();
  }
}

export function getFriendlyErrorMessage(error: string | null | undefined): string | null {
  if (!error) return null;

  // User rejected in MetaMask or similar
  if (
    error.includes("User rejected the request") ||
    error.includes("User denied transaction signature")
  ) {
    return "You rejected the transaction. Please approve it in your wallet to continue.";
  }

  // Add more patterns as needed
  if (error.includes("insufficient funds")) {
    return "You do not have enough funds to complete this transaction.";
  }

  // Fallback: strip technical details if possible
  const firstLine = error.split(".")[0];
  if (firstLine.length < 100) return firstLine;

  // Default fallback
  if (error) {
    return error;
  } else {
    return "An unexpected error occurred. Please try again.";
  }
}
import { User, WallerRetrieveData } from '@/types';
import React, { createContext, useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    useAuthModal,
    useLogout,
    useSignerStatus,
    useSignMessage,
    useSmartAccountClient,
    useUser,
    use<PERSON>hain,
} from "@account-kit/react";
import { web3Client, apiClient } from '@/lib/api';
import Cookies from "js-cookie";
import env from "@/environments/config";

const THINK_TOKEN_KEY = "think_jwt";
const CAN_LOGIN_KEY = 'think_can_login';

// Keys for storing user wallet data in localStorage
export const USER_WALLET_KEY = 'user_wallet_address';
export const USER_ENS_DOMAIN_KEY = 'user_ens_domain';
export const USER_AVATAR_URL_KEY = 'user_avatar_url';
export const USER_SIGNED_MESSAGE_KEY = 'user_signed_message';
export const USER_CHAIN_ID_KEY = 'user_chain_id';
export const USER_CHAIN_NAME_KEY = 'user_chain_name';

interface AuthContextType {
    user: User | null;
    authLogin: (newUser: User) => void;
    authLogout: () => void;
    canLogin: boolean;
    isSigningMessage: boolean;
    assignCanLogin: (canLogin: boolean) => void;
    signMessage: () => Promise<void>;
    chain?: {
        id: number;
        name: string;
    }
}

export const getBearerToken = () => {
    return Cookies.get(THINK_TOKEN_KEY);
}

export const getWalletAddress = () => {
    return localStorage.getItem(USER_WALLET_KEY) as `0x${string}` || undefined;
}

export const getVerifiedSignature = () => {
    return localStorage.getItem(USER_SIGNED_MESSAGE_KEY) as `0x${string}` || undefined;
}

export const getEnsDomain = () => {
    return localStorage.getItem(USER_ENS_DOMAIN_KEY) || undefined;
}

export const getAvatarUrl = () => {
    return localStorage.getItem(USER_AVATAR_URL_KEY) || undefined;
}

export const getChainId = () => {
    return localStorage.getItem(USER_CHAIN_ID_KEY) || undefined;
}

export const getChainName = () => {
    return localStorage.getItem(USER_CHAIN_NAME_KEY) || undefined;
}

const existingUser: User | null = getWalletAddress() && getVerifiedSignature() ? {
    address: getWalletAddress()!,
    ensDomain: getEnsDomain(),
    avatarUrl: getAvatarUrl(),
    userInitialSignedMessage: getVerifiedSignature()
} : null;

const AuthContext = createContext({
    user: existingUser,
    authLogin: (_: User) => { },
    authLogout: () => { },
    canLogin: env.WALLET_LOGIN,
    isSigningMessage: false,
    assignCanLogin: (_: boolean) => { },
    signMessage: () => Promise.resolve(),
    chain: getChainName() ? {
        id: parseInt(getChainId()!),
        name: getChainName()!
    } : undefined
} as AuthContextType);

const getCanLogin = () => {
    return Cookies.get(CAN_LOGIN_KEY);
}

export const AuthProvider = ({ ...props }: React.HTMLAttributes<HTMLDivElement>) => {
    const { logout } = useLogout();
    const accountUser = useUser();
    const { chain } = useChain();
    const [user, setUser] = useState<User | null>(existingUser);
    const [isSigningMessage, setIsSigningMessage] = useState<boolean>(getVerifiedSignature() === null && accountUser !== null);
    const [canLogin, setCanLogin] = useState(getCanLogin() === 'true' || env.WALLET_LOGIN);
    const [chainRef, setChainRef] = useState(chain && {
        id: chain.id,
        name: chain.name
    } || undefined);
    const navigate = useNavigate();

    if (!Cookies.get(CAN_LOGIN_KEY)) {
        Cookies.set(CAN_LOGIN_KEY, env.WALLET_LOGIN.toString());
    }

    const {
        signMessageAsync
    } = useSignMessage({
        client: undefined,
        onSuccess: (signedMessage: string | null) => {
            const storedSignedMessage = getVerifiedSignature();
            console.log("auth signed message", storedSignedMessage, signedMessage, accountUser);
            if (!storedSignedMessage && signedMessage) {
                console.log("logging in after signing");
                localStorage.setItem(USER_SIGNED_MESSAGE_KEY, signedMessage);
            }
            if (accountUser) {
                loginAfterSigning(signedMessage, accountUser);
            }
        },
        onError: (error: any) => {
            console.error('Sign message error:', error);
            localStorage.removeItem(USER_SIGNED_MESSAGE_KEY);
        },
    });

    useEffect(() => {
        // Capture the first time logged in.
        if (!user && accountUser && accountUser.address) {
            console.log("auth login", user, accountUser, accountUser.address);
            authLogin();
        }
    }, [accountUser]);

    useEffect(() => {
        console.log("auth chain", chain);
        if (chain) {
            setChainRef({
                id: chain.id,
                name: chain.name
            });
            localStorage.setItem(USER_CHAIN_ID_KEY, chain.id.toString());
            localStorage.setItem(USER_CHAIN_NAME_KEY, chain.name);
        }
    }, [chain]);

    const loginAfterSigning = async (signedMessage: string | null, newUser: User) => {
        if (signedMessage) {
            newUser.userInitialSignedMessage = signedMessage;
            if (getBearerToken() !== null) {
                // call to assign wallet to backend and get jwt
                const response = await apiClient.authenticateWallet(newUser.address);
                Cookies.set(THINK_TOKEN_KEY, response['token'])
            }
            // call to get wallet data
            const retrieveData: WallerRetrieveData = await web3Client.retrieveWalletData(newUser.address);
            console.log(retrieveData)
            const ensWallet = retrieveData.wallets.find((wallet) => wallet.wallet.use_ens_domain === true);
            const avatarWallet = retrieveData.wallets.find((wallet) => wallet.wallet.use_avatar_url === true);
            if (ensWallet) {
                newUser.ensDomain = ensWallet.wallet.ens_domain;
                localStorage.setItem(USER_ENS_DOMAIN_KEY, ensWallet.wallet.ens_domain);
            }
            if (avatarWallet) {
                newUser.avatarUrl = avatarWallet.wallet.avatar_url;
                localStorage.setItem(USER_AVATAR_URL_KEY, avatarWallet.wallet.avatar_url);
            }
            setUser(newUser);
            setIsSigningMessage(false);
            localStorage.setItem(USER_WALLET_KEY, newUser.address);
            // HACK: force reset for wallet connect
            // HACK: Ideally we can keep a session and still use account key to add wallets.
            // logout();
        }
    }

    const signMessage = async () => {
        await signMessageAsync({ message: `Welcome to Think Agents! This signature is required to verify your wallet ownership.` });
    }

    const authLogin = async () => {
        setIsSigningMessage(true);
        console.log("signing message");
        await signMessageAsync({ message: `Welcome to Think Agents! This signature is required to verify your wallet ownership.` });
    };

    const authLogout = () => {
        console.log("logging out");
        const response = logout();
        console.log("logout response", response);
        setUser(null);
        // Remove user wallet address from localStorage when disconnected
        localStorage.removeItem(USER_WALLET_KEY);
        localStorage.removeItem(USER_ENS_DOMAIN_KEY);
        localStorage.removeItem(USER_AVATAR_URL_KEY);
        localStorage.removeItem(USER_CHAIN_ID_KEY);
        localStorage.removeItem(USER_CHAIN_NAME_KEY);
        localStorage.removeItem(USER_SIGNED_MESSAGE_KEY);
        // call to remove jwt
        Cookies.remove(THINK_TOKEN_KEY);
        setTimeout(() => {
            location.reload();
        }, 50);
    };

    const assignCanLogin = (canLogin: boolean) => {
        setCanLogin(canLogin);
        Cookies.set(CAN_LOGIN_KEY, canLogin.toString());
    }

    const value = { user, isSigningMessage, signMessage, authLogin, authLogout, canLogin, assignCanLogin, chain: chainRef };
    return (
        <AuthContext.Provider value={value}>{props.children}</AuthContext.Provider>
    );
};

export const useAuth = () => {
    return useContext(AuthContext);
};

// Add this utility function for global logout/clear
export function logoutAndClearStorage() {
    console.log("logging out and clearing storage");
    const { logout } = useLogout();
    const r = logout();
    console.log("logout result", r);
    localStorage.removeItem('user_wallet_address');
    localStorage.removeItem('user_ens_domain');
    localStorage.removeItem('user_avatar_url');
    localStorage.removeItem('user_chain_id');
    localStorage.removeItem('user_chain_name');
    localStorage.removeItem('user_signed_message');
    document.cookie = 'think_jwt=; Max-Age=0; path=/;';
    window.location.reload();
}
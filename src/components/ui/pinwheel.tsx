export function PinWheel({ percentage }: { percentage: number }) {
  const filledColor = "#73CEE5";
  const unfilledColor = "#F8F8F8";
  // Define paths in TRUE clockwise order - reorganized to fill clockwise from 12 o'clock
  const paths = [
    // 12 o'clock (straight up)
    "M76.0042 46.1042L77.6658 47.0986V0.5H74.3425V47.0596L76.0042 46.1042Z",

    // 1 o'clock (right side first)
    "M82.4015 49.7937L90.8344 1.94633L87.561 1.36475L79.3358 48.0241L82.4015 49.7937Z",
    "M86.6055 52.2276L103.554 5.65142L100.431 4.51318L83.681 50.5411L86.6055 52.2276Z",

    // 2 o'clock
    "M112.566 9.85647L87.7287 52.8765L90.6068 54.5381L115.444 11.5181L112.566 9.85647Z",
    "M94.6479 56.8639L126.128 19.352L123.586 17.2168L91.7234 55.1773L94.6479 56.8639Z",

    // 3 o'clock (moving right)
    "M98.9931 59.3809L135.292 28.9227L133.157 26.3721L95.9357 57.6112L98.9931 59.3809Z",
    "M102.325 61.3002V63.2194L142.653 39.9396L140.992 37.0649L100.663 60.3447L102.325 61.3002Z",
    "M102.325 68.6863L147.987 52.0697L146.849 48.9458L102.325 65.1553V68.6863Z",
    "M102.325 73.5464L151.136 64.939L150.563 61.6655L102.325 70.1732V73.5464Z",

    // 3 o'clock (horizontal right)
    "M152 74.8433H102.325V78.1666H152V74.8433Z",

    // 4 o'clock (moving down-right)
    "M102.325 82.8273L150.563 91.3349L151.136 88.0615L102.325 79.4541V82.8273Z",
    "M102.325 87.8533L146.849 104.054L147.987 100.931L102.325 84.314V87.8533Z",
    "M102.325 91.7L100.663 92.6637L140.992 115.944L142.653 113.061L102.325 89.7808V91.7Z",

    // 5 o'clock
    "M95.9357 95.3966L133.157 126.627L135.292 124.085L98.9931 93.627L95.9357 95.3966Z",
    "M91.7234 97.8223L123.586 135.791L126.128 133.656L94.6479 96.1357L91.7234 97.8223Z",

    // 6 o'clock (moving toward bottom)
    "M90.6068 98.4638L87.7287 100.125L112.566 143.145L115.444 141.484L90.6068 98.4638Z",
    "M83.681 102.468L100.431 148.487L103.554 147.349L86.6055 100.781L83.681 102.468Z",
    "M79.3358 104.977L87.561 151.636L90.8344 151.063L82.4015 103.208L79.3358 104.977Z",

    // 6 o'clock (straight down)
    "M76.0042 106.904L74.3425 105.94V152.5H77.6658V105.94L76.0042 106.904Z",

    // 7 o'clock (moving down-left)
    "M69.6068 103.208L61.1656 151.063L64.439 151.636L72.6642 104.977L69.6068 103.208Z",
    "M65.3945 100.781L48.4456 147.349L51.5695 148.487L68.319 102.468L65.3945 100.781Z",
    "M61.3958 98.4717L36.5582 141.492L39.4362 143.153L64.2738 100.133L61.3958 98.4717Z",

    // 8 o'clock
    "M57.3521 96.1357L25.872 133.656L28.4226 135.791L60.2766 97.8223L57.3521 96.1357Z",
    "M53.0068 93.627L16.7163 124.085L18.8515 126.627L56.0726 95.3966L53.0068 93.627Z",

    // 9 o'clock (moving left)
    "M49.6752 91.7V89.7808L9.35512 113.061L11.0168 115.944L51.3369 92.6637L49.6752 91.7Z",
    "M49.6752 84.314L4.01291 100.931L5.15114 104.054L49.6752 87.8533V84.314Z",
    "M49.6752 79.4541L0.864059 88.0615L1.44564 91.3349L49.6752 82.8356V79.4541Z",

    // 9 o'clock (horizontal left)
    "M49.6752 74.8433H0V78.1666H49.6752V74.8433Z",

    // 10 o'clock (moving up-left)
    "M49.6752 70.1732L1.44564 61.6655L0.864059 64.939L49.6752 73.5464V70.1732Z",
    "M49.6752 65.1469L5.15114 48.9458L4.01291 52.0697L49.6752 68.6863V65.1469Z",
    "M49.6752 61.3002L51.3369 60.3447L11.0168 37.0649L9.35512 39.9396L49.6752 63.2194V61.3002Z",

    // 11 o'clock (left side)
    "M56.0726 57.6112L18.8515 26.3721L16.7163 28.9227L53.0068 59.3726L56.0726 57.6112Z",
    "M60.2766 55.1773L28.4226 17.2168L25.872 19.352L57.3521 56.8639L60.2766 55.1773Z",
    "M39.4445 9.84664L36.5664 11.5083L61.404 54.5283L64.2821 52.8666L39.4445 9.84664Z",
    "M68.319 50.5328L51.5695 4.51318L48.4456 5.65142L65.3945 52.2193L68.319 50.5328Z",
    "M72.6642 48.0241L64.439 1.36475L61.1656 1.94633L69.6068 49.7937L72.6642 48.0241Z",
  ];

  // Calculate how many paths should be filled based on percentage
  const totalPaths = paths.length;
  const filledPathCount = Math.floor((percentage / 100) * totalPaths);

  return (
    <>
      <svg xmlns="http://www.w3.org/2000/svg" width="152" height="153" viewBox="0 0 152 153" fill="none">
        <g clip-path="url(#clip0_3721_11236)">
          {paths.map((pathData, index) => (
            <path
              key={index}
              d={pathData}
              fill={index < filledPathCount ? filledColor : unfilledColor}
            />
          ))}
        </g>

        {/* Percentage text in the center */}
        <text
          x="76"
          y="80"
          textAnchor="middle"
          dominantBaseline="middle"
          fill="white"
          fontSize="18"
          fontWeight="300"
          fontFamily='ibm-plex-mono, monospace'
        >
          {percentage}%
        </text>

        <defs>
          <clipPath id="clip0_3721_11236">
            <rect width="152" height="152" fill="white" transform="translate(0 0.5)" />
          </clipPath>
        </defs>
      </svg>
    </>
  )
}
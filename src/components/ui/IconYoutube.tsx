function IconYoutube({ className}: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <svg width="38" height="26" viewBox="0 0 38 26" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
            <path d="M36.4354 4.13021C37.2479 6.97396 37.2479 13.0677 37.2479 13.0677C37.2479 13.0677 37.2479 19.0938 36.4354 22.0052C36.0291 23.6302 34.7427 24.849 33.1854 25.2552C30.2739 26 18.7635 26 18.7635 26C18.7635 26 7.18538 26 4.27392 25.2552C2.71663 24.849 1.43018 23.6302 1.02393 22.0052C0.211426 19.0938 0.211426 13.0677 0.211426 13.0677C0.211426 13.0677 0.211426 6.97396 1.02393 4.13021C1.43018 2.50521 2.71663 1.21875 4.27392 0.8125C7.18538 0 18.7635 0 18.7635 0C18.7635 0 30.2739 0 33.1854 0.8125C34.7427 1.21875 36.0291 2.50521 36.4354 4.13021ZM14.9718 18.5521L24.5864 13.0677L14.9718 7.58333V18.5521Z" fill="currentColor"/>
        </svg>
    );
}

export { IconYoutube };
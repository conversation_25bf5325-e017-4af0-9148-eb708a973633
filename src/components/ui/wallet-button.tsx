import {
  useAuthModal
} from "@account-kit/react";
import { useEffect, useState } from 'react';
import { Button, ButtonSizes, ButtonVariants } from './button';
import { WalletInfoModal } from './wallet-info-modal';
import { cn } from "@/lib/utils";
import { useAuth } from '../auth/AuthContext';
import { useNavigate } from "react-router";
import env from "@/environments/config";


export interface WalletButtonProps {
  showLoginModal?: boolean;
  addWallet?: boolean;
  hideOnLogin?: boolean;
  size?: ButtonSizes;
  className?: string;
  variant?: ButtonVariants;
  loggedOutButton?: JSX.Element | null;
}

export const WalletButton = ({ 
  showLoginModal = true, 
  hideOnLogin = false, 
  size = 'default', 
  addWallet = false, 
  className = '', 
  variant = 'outlineGradient',
  loggedOutButton = null
}: WalletButtonProps) => {
  const { user, canLogin, signMessage, isSigningMessage } = useAuth();
  const navigate = useNavigate();
  const { openAuthModal } = useAuthModal();
  // const { account, connect, disconnect } = useAlchemyAccount();
  // const { connect } = useConnect();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [walletUser, setWalletUser] = useState(user);

  if (!canLogin || (!user && loggedOutButton) || !showLoginModal) {
    return loggedOutButton;
  }

  useEffect(() => {
    setWalletUser(user);
  }, [user]);

  if (isSigningMessage) {
    return (
      <Button
        onClick={() => signMessage()}
        variant={variant}
        size={size}
        className={className}
      >
        Sign Message
      </Button>
    );
  } else if (!walletUser || addWallet) {
    return (
      <Button
        onClick={() => openAuthModal()}
        variant={variant}
        size={size}
        className={className}
      >
        {addWallet ? 'Add Wallet' : 'Connect Wallet' }
      </Button>
    );
  }

  let ensName = walletUser.ensDomain;
  if (ensName && ensName.length > 15) {
    ensName = `${ensName.slice(0, 10)}...${ensName.slice(-5)}`
  }

  if (hideOnLogin) {
    return null;
  }

  return (
    <>
      <Button
        onClick={() => navigate("/dashboard")}
        variant={variant}
        className={"!h-[55px] !px-2 hover:bg-gradient-to-b rounded-full text-center overflow-hidden hover:ring-0 hover:from-teal hover:to-sand text-white hover:text-black"}
      >
        <div className="flex items-center gap-1 md-py-5">
          <div className="flex flex-col ring ring-2 ring-inset ring-teal rounded-full overflow-hidden">
            {walletUser.avatarUrl ? <img src={walletUser.avatarUrl} className="p-0.5 w-10 h-10 rounded-full" /> : null}
          </div>
          <div className=" text-base md:text-base flex flex-col ml-2 md-mr-8">
            <div className="flex flex-row">
              {ensName ? ensName : null}
            </div>
            <div className="font-mono text-2xs !leading-[10px] text-[#909090] flex flex-row">
              {walletUser.address
                ? `${walletUser.address.slice(0, 6)}...${walletUser.address.slice(-4)}`
                : 'Connected'
              }
            </div>
          </div>
        </div>
      </Button>
      <WalletInfoModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
};
import { cn } from "@/lib/utils";
import { Button } from "./button";
import { useEffect, useState } from "react";
import { formatTime } from "@/time-configs/TGE-configs";

export interface CountdownTimerProps {
    className?: string;
    title: string;
    from: Date;
    to: Date;
    buttonLabel?: string;
    action?: () => void;
}

function CoundownTimer({ 
    className,
    title,
    from,
    to,
    buttonLabel,
    action
 }: CountdownTimerProps): JSX.Element {
    const timeLeft = to.getTime() - new Date().getTime();
    const [secondsLeft, setSecondsLeft] = useState(timeLeft > 0 ? timeLeft / 1000 : 0);
    const [intervalId, setIntervalId] = useState<NodeJS.Timeout | undefined>();
    const adjustedStart = new Date().getTime() - from.getTime();
    const adjustedEnd = to.getTime() - from.getTime();

    useEffect(() => {
        if (secondsLeft > 0) {
            setIntervalId(setTimeout(() => {
                setSecondsLeft(secondsLeft - 1);
            }, 1000));
        }

        return () => clearTimeout(intervalId); // Cleanup on unmount or when secondsLeft is 0
    }, [secondsLeft]);

    const progressAsPercentage = adjustedStart / adjustedEnd * 100.0;
    // console.log("progress", progressAsPercentage);
    return (
        <div className={cn(className, "max-w-[100vw] w-full px-4 lg:px-8 xl:px-10 2xl:px-16 3xl:px-24 py-2 lg:py-4 bg-black py-2  md:flex align-center flex-row gap-4")}>
            <div className="flex-none hidden lg:block">
                <div className="flex-column justify-center h-full flex items-center">
                    <img src="/images/knocked-out-coin.svg" className="w-auto h-6 sm:h-10" />
                </div>
            </div>
            <div className="flex-none font-bold font-mono uppercase text-teal">
                <div className="flex-column justify-center h-full flex items-center">{title}</div>
            </div>
            <div className="flex-none font-bold font-mono uppercase text-white">
                <div className="flex-column justify-center h-full flex items-center">{formatTime(secondsLeft)}</div>
            </div>
            <div className="grow font-bold uppercase font-mono text-white py-4 md:py-0 ">
                <div className="flex-column justify-center h-full flex items-center">
                    <div className="w-full h-[5px] bg-white rounded-full relative overflow-hidden">
                        <div 
                            className="h-full absolute left-0 bg-gradient-to-r from-[#0CC1DA] from-[0%] to-[#F4E6BA] to-[50%] to-[#986513] to-[74%] to-[#DEAB46] to-[100%]" 
                            style={{ width: `${progressAsPercentage}%` }}
                        ></div>
                    </div>
                </div>
            </div>
            { buttonLabel ? (
                <div className="flex-none hidden md:block">
                    <Button variant="gradient" className="rounded-lg" onClick={action}>
                        {buttonLabel}
                    </Button>
                </div>
            ) : null}
        </div>
    );
}

export {CoundownTimer};
import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

export type ButtonVariants = 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'reverseGradient' | 'gradient' | 'outlineGradient';
export type ButtonSizes = 'default' | 'sm' | 'lg' | 'full' | 'icon';

const buttonVariants = cva(
    "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
    {
        variants: {
            variant: {
                reverseGradient: 
                    "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground rounded-full text-center font-semibold text-base md:text-base overflow-hidden text-sm !leading-[34px] hover:ring-0 hover:from-teal hover:to-sand hover:text-black px-2 md:px-5 ring ring-1 ring-inset ring-teal hover:bg-gradient-to-b text-white ",
                gradient:
                    "transition-none rounded-full border border-input shadow-sm px-2 md:px-5 py-3 ring ring-1 ring-inset ring-teal bg-gradient-to-b text-black from-[#21D0FC] from-[23.44%] to-[#ECDCAA] to-[77.6%] hover:bg-[#21D0FC] duration-300 hover:bg-accent hover:from-[#000000] hover:to-[#000000] hover:text-white",
                outlineGradient:
                    "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground rounded-full text-center font-semibold text-base md:text-base overflow-hidden text-sm !leading-[34px] hover:ring-0 hover:from-teal hover:to-sand hover:text-black",  
                default:
                    "bg-primary text-primary-foreground shadow hover:bg-primary/90",
                destructive:
                    "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
                outline:
                    "rounded-full  border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",
                secondary:
                    "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
                ghost: "hover:bg-accent hover:text-accent-foreground",
                link: "text-primary underline-offset-4 hover:underline",
            } as { [key in ButtonVariants]: string },
            size: {
                default: "h-9 px-4 py-5",
                sm: "h-8 rounded-md px-3 text-xs",
                lg: "h-10 rounded-md px-8",
                full: "w-full",
                icon: "size-[30px] rounded-md",
            } as { [key in ButtonSizes]: string },
        },
        defaultVariants: {
            variant: "default",
            size: "default",
        },
    }
);


export interface ButtonProps
    extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
    asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({ className, variant, size, asChild = false, ...props }, ref) => {
        const Comp = asChild ? Slot : "button";
        return (
            <Comp
                type="button"
                className={cn(buttonVariants({ variant, size, className }))}
                ref={ref}
                {...props}
            />
        );
    }
);
Button.displayName = "Button";

const GradientButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({ className, variant, size, asChild = false, ...props }, ref) => {
        const Comp = asChild ? Slot : "button";
        return (
            <Comp
                type="button"
                className="relative text-black uppercase px-2 py-1 rounded-lg font-medium transition-all duration-300 text-sm lg:text-base hover:shadow-lg"
                ref={ref}
                style={{
                    border: '1px solid transparent',
                    background: 'linear-gradient(180deg, #21D0FC 23.44%, #ECDCAA 77.6%) padding-box, linear-gradient(180deg, #ECDCAA 23.44%, #21D0FC 77.6%) border-box',
                    borderRadius: '8px'
                }}
                {...props}
            />
        );
    }
);

GradientButton.displayName = "GradientButton";

const GrayGradientButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({ className, variant, size, asChild = false, ...props }, ref) => {
        const Comp = asChild ? Slot : "button";
        return (
            <Comp
                type="button"
                className="relative text-white uppercase px-2 py-1 rounded-lg font-medium transition-all duration-300 text-sm lg:text-base hover:shadow-lg"
                ref={ref}
                style={{
                    border: '1px solid transparent',
                    background: 'linear-gradient(145deg, rgb(37, 39, 45) 0%, #5D6B75 50%, rgb(37, 39, 45) 100%) padding-box, radial-gradient(100% 100% at 50% 50%, #0E0F12 0%, #5D6B75 100%) border-box',
                    borderRadius: '8px'
                }}
                {...props}
            />
        );
    }
);


GrayGradientButton.displayName = "GrayGradientButton";


const GrayGradientButtonIneligible = React.forwardRef<HTMLButtonElement, ButtonProps>(
    ({ className, variant, size, asChild = false, ...props }, ref) => {
        const Comp = asChild ? Slot : "button";
        return (
            <Comp
                onClick={() => {}}
                type="button"
                className="relative text-white uppercase px-2 py-1 rounded-lg font-small transition-all duration-300 text-sm lg:text-base hover:shadow-lg cursor-not-allowed"
                ref={ref}
                style={{
                    border: '1px solid transparent',
                    backgroundImage: 'linear-gradient(black, black), linear-gradient(145deg,rgb(37, 39, 45) 0%, #5D6B75 50%, rgb(37, 39, 45) 100%)',
                    backgroundClip: 'padding-box, border-box',
                    backgroundOrigin: 'border-box',
                    borderRadius: '8px'
                }}
                {...props}
            />
        );
    }
);


GrayGradientButtonIneligible.displayName = "GrayGradientButtonIneligible";

export { Button, buttonVariants, GradientButton, GrayGradientButton, GrayGradientButtonIneligible };

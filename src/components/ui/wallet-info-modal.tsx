import { useLogout, useUser } from "@account-kit/react";
import { <PERSON><PERSON> } from "./button";
import {
  Di<PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "./dialog";
import { Copy, ExternalLink, LogOut } from "lucide-react";
import { useState } from "react";
import { USER_WALLET_KEY, USER_SIGNED_MESSAGE_KEY } from "@/components/auth/AuthContext";

interface WalletInfoModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function WalletInfoModal({ isOpen, onClose }: WalletInfoModalProps) {
  const user = useUser();
  const { logout } = useLogout();
  const [isCopied, setIsCopied] = useState(false);

  if (!user) return null;

  const handleLogout = () => {
    // Remove user wallet address and signed message from localStorage when logging out
    localStorage.removeItem(USER_WALLET_KEY);
    localStorage.removeItem(USER_SIGNED_MESSAGE_KEY);
    logout();
    onClose();
  };

  const handleCopyAddress = () => {
    if (user.address) {
      navigator.clipboard.writeText(user.address);
      setIsCopied(true);
      setTimeout(() => setIsCopied(false), 2000);
    }
  };

  const getEtherscanLink = () => {
    // Assuming Ethereum mainnet, but you might want to dynamically determine the network
    return `https://etherscan.io/address/${user.address}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Wallet Information</DialogTitle>
          <DialogDescription>
            Your connected wallet details and options
          </DialogDescription>
        </DialogHeader>

        <div className="p-4 space-y-4">
          <div className="flex flex-col space-y-1">
            <span className="text-sm font-medium text-muted-foreground">
              Connected Address
            </span>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium break-all">
                {user.address}
              </span>
              <Button
                variant="ghost"
                size="icon"
                className="ml-2"
                onClick={handleCopyAddress}
              >
                <Copy className="w-4 h-4" />
                <span className="sr-only">Copy address</span>
              </Button>
            </div>
            {isCopied && (
              <span className="text-xs text-green-500">
                Address copied to clipboard
              </span>
            )}
          </div>

          <div className="flex flex-col space-y-1">
            <a
              href={getEtherscanLink()}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center text-sm text-blue-500 hover:underline"
            >
              <span>View on Etherscan</span>
              <ExternalLink className="ml-1 w-3 h-3" />
            </a>
          </div>
        </div>

        <DialogFooter className="sm:justify-between">
          <Button
            variant="outline"
            onClick={onClose}
          >
            Close
          </Button>
          <Button
            variant="destructive"
            onClick={handleLogout}
            className="flex items-center gap-2"
          >
            <LogOut className="w-4 h-4" />
            Logout
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
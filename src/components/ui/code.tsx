import { cn } from "@/lib/utils";
import React from "react";

interface CodeProps extends React.HTMLAttributes<HTMLPreElement> {
  children: React.ReactNode;
}

export function Code({ className, children, ...props }: CodeProps) {
  return (
    <pre
      className={cn(
        "rounded-md bg-muted p-2 font-mono text-sm overflow-auto",
        className
      )}
      {...props}
    >
      <code>{children}</code>
    </pre>
  );
}
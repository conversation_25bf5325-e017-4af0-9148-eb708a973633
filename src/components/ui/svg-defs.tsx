import { cn } from "@/lib/utils";

function SVGDefs({
    className
}: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <svg aria-hidden="true" focusable="false" className={cn("w-0 h-0", className)}>
          <defs>
            <linearGradient id="cart-icon-gradient" x1="21.4949" y1="38.5583" x2="21.4949" y2="9.36797" gradientUnits="userSpaceOnUse">
              <stop stopColor="#F4E6BA"/>
              <stop offset="1" stopColor="#0CC1DA"/>
            </linearGradient>
        
            <linearGradient id="gem-icon-large-gradient" x1="43.5679" y1="105.507" x2="43.5679" y2="-0.813001" gradientUnits="userSpaceOnUse">
              <stop stopColor="#EDDCAA"/>
              <stop offset="0.14" stopColor="#E9DBAB"/>
              <stop offset="0.28" stopColor="#DDDBB0"/>
              <stop offset="0.41" stopColor="#C9D9B8"/>
              <stop offset="0.55" stopColor="#ADD8C3"/>
              <stop offset="0.69" stopColor="#89D6D2"/>
              <stop offset="0.83" stopColor="#5CD3E4"/>
              <stop offset="0.96" stopColor="#29D0F8"/>
              <stop offset="1" stopColor="#1AD0FF"/>
            </linearGradient>
        
            <linearGradient id="gem-icon-small-gradient" x1="10.1069" y1="24.3244" x2="10.1069" y2="0.698411" gradientUnits="userSpaceOnUse">
              <stop stopColor="#EDDCAA"/>
              <stop offset="0.14" stopColor="#E9DBAB"/>
              <stop offset="0.28" stopColor="#DDDBB0"/>
              <stop offset="0.41" stopColor="#C9D9B8"/>
              <stop offset="0.55" stopColor="#ADD8C3"/>
              <stop offset="0.69" stopColor="#89D6D2"/>
              <stop offset="0.83" stopColor="#5CD3E4"/>
              <stop offset="0.96" stopColor="#29D0F8"/>
              <stop offset="1" stopColor="#1AD0FF"/>
            </linearGradient>
        
            <clipPath id="gem-icon-large-clip">
              <rect width="88" height="99" fill="white"/>
            </clipPath>
        
            <clipPath id="gem-icon-small-clip">
              <rect width="20" height="23" fill="white"/>
            </clipPath>
          </defs>
        </svg>
    );
}

export { SVGDefs };
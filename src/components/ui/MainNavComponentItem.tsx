import { cn } from "@/lib/utils";
import { NavLink } from "react-router";

interface MainNavItemProps extends React.HTMLAttributes<HTMLAnchorElement> {
    to: string;
    active?: boolean;
    isChild?: boolean;
    onClick?: () => void | undefined;
    title?: string;
    target?: string;
}

const MainNavComponentItem: React.FC<MainNavItemProps> = ({
    className,
    target,
    isChild,
    onClick,
    active = true,
    title,
    to
}) => {
    return (
        <NavLink to={to} onClick={onClick ? onClick : undefined} target={target} className={cn(`flex items-center justify-between group hover:text-teal ${active ? 'text-white' : 'text-sand'}`, className)}>
            <span className={`${isChild ? "" : "group-hover:text-teal"} transition`}>{title}</span>
        </NavLink>

    );
}

export { MainNavComponentItem };
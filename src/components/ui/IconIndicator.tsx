function IconIndicator({ className}: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" className={className} xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_3952_2045)">
                <circle cx="7.00008" cy="6.99984" r="5.83333" stroke="#5D5D5D" strokeWidth="1.5" />
                <path d="M7 9.9165V6.4165" stroke="#5D5D5D" strokeWidth="1.5" strokeLinecap="round" />
                <circle cx="0.583333" cy="0.583333" r="0.583333" transform="matrix(1 0 0 -1 6.41675 5.25)" fill="#5D5D5D" />
            </g>
            <defs>
                <clipPath id="clip0_3952_2045">
                    <rect width="14" height="14" fill="white" />
                </clipPath>
            </defs>
        </svg>


    );
}

export { IconIndicator };
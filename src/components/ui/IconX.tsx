function IconX({ className}: React.HTMLAttributes<HTMLDivElement>) {
    return (
        <svg width="30" height="26" viewBox="0 0 30 26" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
            <path d="M23.1006 0H27.5113L17.8753 11.0133L29.2113 26H20.3353L13.3833 16.9107L5.4286 26H1.01527L11.3219 14.22L0.447266 0H9.5486L15.8326 8.308L23.1006 0ZM21.5526 23.36H23.9966L8.2206 2.50133H5.59793L21.5526 23.36Z" fill="currentColor"/>
        </svg>

    );
}

export { IconX };
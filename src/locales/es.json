{"header": {"navigation": {"about": "Acerca de", "thinkProtocol": "Protocolo THINK", "resources": "Recursos", "contact": "Contacto"}, "dropdown": {"about": {"team": "Equipo", "partners": "<PERSON><PERSON><PERSON>", "investors": "Inversores"}, "thinkProtocol": {"thinkToken": "Token THINK", "thinkBuilders": "Constructores THINK"}, "resources": {"docs": "Documentación", "whitepaper": "Libro Blanco", "faqs": "Preguntas Frecuentes"}}, "buttons": {"mintAnnouncement": "<PERSON><PERSON><PERSON>", "joinTheMovement": "Únete al Movimiento", "claimThink": "Reclamar $THINK"}, "countdown": {"foundersBonus": "El Bono de Fundadores Termina En"}, "footer": {"privacy": "Tu Privacidad", "terms": "Términos y Condiciones"}, "accessibility": {"openMainMenu": "<PERSON><PERSON><PERSON> principal", "closeMenu": "<PERSON><PERSON><PERSON>"}, "socialMedia": {"twitter": "X (Twitter)", "discord": "Discord", "youtube": "YouTube", "magicEden": "Magic Eden"}}, "pages": {"home": {"hero": {"title": "IA que Posees", "subtitle": "THINK es el protocolo abierto para agentes de IA que puedes poseer, evolucionar y personalizar."}, "features": {"title": "Beneficios <PERSON> Think", "subtitle": "Conecta con agentes de código abierto, herramientas y MCPs en un marco composable.", "description": "THINK conecta agentes de IA a una red creciente de herramientas, modelos y protocolos de código abierto, haciéndolos más inteligentes, más interoperables y completamente propiedad del usuario. Construido para desarrolladores, creadores y comunidades, THINK es la base de una nueva internet impulsada por agentes, donde la inteligencia es composable, los datos permanecen con el usuario y la innovación es sin permisos. Ya sea que estés construyendo un solo agente o un ecosistema completo, THINK es el protocolo que hace que todo funcione juntos. Nosotros, el pueblo, debemos elegir el camino de la IA. Debemos asegurar que nunca se convierta en una herramienta utilizada por unos pocos para manipular, controlar y finalmente esclavizar las mentes de muchos. La descentralización es nuestro escudo contra esta tiranía: es como salvaguardamos el pensamiento libre y prevenimos el surgimiento de tiranos digitales.", "professionalAgents": {"title": "Agentes Profesionales", "description": "Impulsa entidades de IA adaptativas que interoperan entre plataformas, adquiriendo nuevas habilidades y atributos dondequiera que vayan."}, "ownExperience": {"title": "Posee Tu Experiencia", "description": "Desde la creación de agentes hasta las transacciones en el juego, $THINK te da control real sobre cómo evolucionan e interactúan los agentes."}, "patentProtection": {"title": "Protección de Patentes contra Trolls", "description": "Innova con confianza bajo el paraguas de patentes de $THINK, protegido de reclamos maliciosos de infracción para que puedas enfocarte en construir."}, "seamlessInteroperability": {"title": "Interoperabilidad Perfecta", "description": "Los agentes llevan su personalidad, datos, conciencia ambiental y activos a través de ecosistemas, fomentando la colaboración fluida e identidades persistentes."}}}, "about": {"title": "ACERCA DE", "hero": {"title": "Constructores de la web agéntica", "subtitle": "Somos un grupo de ingenieros, diseñadores y pensadores de sistemas con una misión compartida:", "mission": "permitir que las personas piensen por sí mismas dándoles propiedad sobre la IA que usan todos los días."}, "description": {"paragraph1": "Nuestros antecedentes abarcan infraestructura de IA, protocolos blockchain, ecosistemas de código abierto y diseño de productos de clase mundial. Hemos ayudado a escalar plataformas globales, enviar herramientas queridas y arquitectar sistemas que priorizan la libertad del usuario sobre el control corporativo. No solo estamos construyendo un protocolo: estamos diseñando la base para una nueva internet.", "paragraph2": "Una donde los agentes son soberanos, la inteligencia es composable y la innovación es sin permisos.", "paragraph3": "Creemos que el próximo gran salto en la computación es hacer sistemas de IA inteligentes que las personas realmente posean."}, "team": {"title": "Nuestro Equipo"}}, "contact": {"title": "CONTACTO", "subtitle": "<PERSON><PERSON><PERSON> por tu interés en construir con $THINK. Por favor cuéntanos sobre tu proyecto a continuación:", "form": {"fullName": "Nombre Completo", "email": "Correo Electrónico", "twitterHandle": "Usuario de Twitter", "message": "Men<PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON><PERSON>", "submitting": "Enviando..."}, "errors": {"generic": "<PERSON> sentimos, recibimos un error. <PERSON><PERSON> favor intenta de nuevo más tarde. Si continúas teniendo problemas, por favor envíanos un mensaje en Twitter."}}, "dashboard": {"greeting": "<PERSON><PERSON> {{name}},", "greetingDefault": "Hola,", "greetingLoading": "<PERSON><PERSON> {{name}},", "greetingLoadingDefault": "Hola,", "yourRewards": "<PERSON><PERSON>pen<PERSON>", "availableToStake": "Disponible para Apostar", "claimAndRestake": "Reclamar y Reapostar", "ineligible": "No Elegible", "notImplemented": "no implementado", "foundersBonus": "El Bono de Fundadores Termina En"}, "thinkubator": {"navigation": {"projects": "Proyectos", "events": "Eventos", "faq": "Preguntas Frecuentes"}, "hero": {"title": "Creciendo la capa de aplicaciones tokenizadas", "season": "Temporada Uno:", "seasonTime": "Verano 2025"}, "projects": {"seasonOneCohort": "Cohorte de la Temporada Uno", "description": "Esta es tu vista de primera fila al primer lote de proyectos de cohorte y asociaciones. Nuestra primera cohorte expandirá lo que es posible para los agentes THINK. Probando los límites. Estableciendo el estándar. Mantente al día con los proyectos en la cohorte de la Temporada 1 a continuación.", "types": {"personalFinanceVault": "Bóveda de Finanzas Personales", "agentGaming": "Juegos de Agentes", "generativeImageVideo": "Herramientas de Imagen y Video Generativo"}, "names": {"redacted": "[censurado]", "augmentedImagination": "Imaginación Aumentada"}}, "about": {"description": "Cultivamos Aplicaciones Tokenizadas, o App Coins para abreviar, para gamificar la contribución de código abierto y la expansión de la comunidad. Los proyectos exitosos apuntarán a obtener sus primeros 1000 usuarios durante esta temporada. El mejor software de la era de la inteligencia puede ser nuestro.", "readFaq": "Leer Preguntas Frecuentes de Thinkubator"}, "events": {"title": "Eventos", "openSourcers": {"title": "Desarrolladores de Código Abierto", "description": "Grupo semanal de constructores para construir en THINK. Nos reunimos los jueves al mediodía del Pacífico.", "joinDiscord": "Únete a nosotros en Discord"}}, "seasonTwo": {"title": "Cohorte de la Temporada Dos", "subtitle": "Otoño 2025 — Comienza a principios de octubre", "description": "¿Interesado en unirte a la próxima temporada? Esta es tu oportunidad de pasar de la idea a la realidad con el apoyo del ecosistema de THINK. Estamos buscando fundadores, hackers y soñadores que estén construyendo el futuro de la inteligencia. ¡Queremos saber de ti!", "applicationsDue": "Solicitudes vencen el 31 de julio de 2025", "teamsSelected": "Equipos seleccionados a principios de septiembre", "applyNow": "Aplica<PERSON>"}, "errors": {"notEnabled": "Thinkubator no está habilitado"}}, "notFound": {"title": "404", "subtitle": "IA que Posees"}, "faq": {"title": "Preguntas Frecuentes"}, "blog": {"title": "BLOG"}, "investors": {"title": "INVERSORES", "hero": {"title": "Construyendo el protocolo fundamental para la IA descentralizada.", "description": "Estamos creando un futuro donde la IA no esté encerrada dentro de plataformas, sino que evolucione en manos de sus usuarios. Los agentes son propiedad de los usuarios, impulsados por inteligencia de código abierto y conectados a través de infraestructura componible. Si estás alineado con esa visión, nos encantaría hablar."}}, "partners": {"title": "SOCIOS", "hero": {"title": "El Instituto de IA Independiente está haciendo de la IA independiente el estándar global", "description1": "Think Independent AI Institute es una iniciativa innovadora que reúne proyectos líderes, protocolos y contribuyentes en el espacio de IA descentralizada.", "description2": "Aprende más sobre el Instituto de IA Independiente", "linkText": "aquí"}}, "claim": {"title": "Reclamar $THINK", "subtitle": "IA que Posees", "faqTitle": "Preguntas Frecuentes sobre Reclamos", "benefitsTitle": "Beneficios <PERSON> Think", "faqs": {"whatIsThink": {"question": "¿Qué es Think?", "answer": "Quisque scelerisque auctor dolor, nec rhoncus dui. <PERSON><PERSON><PERSON> et mattis nunc. Mauris vestibulum rhoncus lorem, ac pellentesque arcu aliquam vel. Nulla vitae iaculis sapien. Cras interdum, quam non lobortis consectetur, lectus massa molestie nisl, ac finibus metus tortor eget sapien. Nam a lacus commodo, ultricies metus id, egestas ipsum. Nullam sodales nunc metus, vel gravida nisi porttitor ac. Pellentesque ac leo pulvinar, pellentesque mi a.\n\nProin non sagittis dolor. Nullam pellentesque risus quis quam convallis pellentesque. Nunc eu justo at erat ultricies pellentesque. Fusce at rutrum velit, sit amet fringilla ipsum. Nulla ligula velit, viverra eu accumsan pellentesque, efficitur ut massa."}, "howDifferentFromAsto": {"question": "¿En qué se diferencia esto de ASTO?", "answer": ""}, "whoBehindThink": {"question": "¿Quién está detrás de Think?", "answer": ""}, "howManyTokens": {"question": "¿Cuántos tokens serán lanzados?", "answer": ""}, "howCanIBuild": {"question": "¿Cómo puedo construir con esto?", "answer": ""}, "canIStake": {"question": "¿puedo apostar?", "answer": ""}, "whatIsStaking": {"question": "¿Qué es apostar?", "answer": ""}, "whyCantWithdraw": {"question": "¿Por qué no puedo retirar hoy?", "answer": ""}}, "benefits": {"professionalAgents": {"title": "Agentes Profesionales", "description": "Impulsa entidades de IA adaptativas que interoperan entre plataformas, adquiriendo nuevas habilidades y atributos dondequiera que vayan."}, "ownExperience": {"title": "Posee Tu Experiencia", "description": "Desde la creación de agentes hasta las transacciones en el juego, $THINK te da control real sobre cómo evolucionan e interactúan los agentes."}, "patentProtection": {"title": "Protección de Patentes Contra Trolls", "description": "Innova con confianza bajo el paraguas de patentes de $THINK, protegido de reclamos maliciosos de infracción para que puedas enfocarte en construir."}, "seamlessInteroperability": {"title": "Interoperabilidad Perfecta", "description": "Los agentes llevan su personalidad, datos, conciencia ambiental y activos a través de ecosistemas, fomentando la colaboración fluida e identidades persistentes."}}}}, "dialogs": {"goldenTicket": {"title": "Poseedor de Boleto Dorado", "description": "¡Felicidades! Eres un Poseedor de Boleto Dorado. ¡Gracias por obtener un paquete de agente think! Recibirás {{amount}} $THINK adicionales.", "viewTransaction": "Ver Transacción en", "etherscan": "Etherscan"}, "astoConversion": {"title": "Convertir $ASTO a $THINK", "approving": "Aprobando ASTO...", "approved": "ASTO Aprobado", "burning": "Quemando ASTO...", "success": "¡Quema Exitosa!", "error": "Error de Transacción", "swappingDisabled": "Intercambio Deshabilitado", "swappingDisabledDescription": "El intercambio está actualmente deshabilitado. Contacta a Discord si crees que esto es un error.", "termsAgreement": "Al continuar aceptas nuestros", "termsOfService": "términos de servicio", "privacyPolicy": "política de privacidad", "youAreBurning": "ESTÁS QUEMANDO", "youAreReceiving": "ESTÁS RECIBIENDO", "twoTransactions": "Esto requiere dos transacciones: una para aprobar el gasto de ASTO, y otra para realizar la quema.", "approvingSpend": "Aprobando gasto de ASTO...", "processingTransaction": "Procesando transacción, por favor espera...", "burningForThink": "Quemando ASTO por THINK...", "approvalSuccessful": "¡Aprobación de ASTO exitosa!", "proceedToBurn": "Ahora puedes proceder a quemar tu ASTO por $THINK.", "burnSuccessful": "¡Quema de ASTO exitosa! Has recibido $THINK.", "approveAsto": "Aprobar ASTO", "confirmBurn": "Confirmar <PERSON>", "transactionInProgress": "Transacción en Progreso", "viewTransaction": "Ver Transacción"}, "stake": {"titleStake": "Apostar $THINK", "titleUnstake": "Desapostar $THINK", "stakingDisabled": "Apuestas Deshabilitadas", "stakingDisabledDescription": "Las apuestas están actualmente deshabilitadas.", "reachOutDiscord": "Contacta a discord si crees que esto es un error.", "transactionSuccessful": "Transacción Exitosa", "youHaveStaked": "<PERSON><PERSON> tienes {{amount}} $THINK apostados.", "stakeThink": "Apostar Think", "approvedForStaking": "Has aprobado {{amount}} $THINK para apostar. <PERSON><PERSON> puedes proceder a apostar tu $THINK.", "transactionUnsuccessful": "Transacción No Exitosa", "errorOccurred": "Ha ocurrido un error durante la transacción. Por favor intenta de nuevo.", "youAreClaimingAll": "Estás reclamando todo $THINK", "approvingAllowance": "Aprobando Asignación", "allowanceApproved": "Asignación Aprobada", "confirmingAllowance": "Confirmando Asignación", "stakingThink": "Apostando $THINK", "canTakeMinutes": "Esto puede tomar unos minutos. Por favor espera mientras se completa la transacción.", "youWillStake": "Apostarás {{amount}} $THINK.", "insufficientThink": "$THINK Insuficiente", "purchaseMoreThink": "Por favor compra más $THINK para apostar y regresa aquí para ganar recompensas.", "inProgress": "en Progreso", "approveAllowance": "<PERSON><PERSON><PERSON>", "tryAgain": "Intentar de Nuevo", "errorClickReload": "Error. <PERSON><PERSON> clic para Recargar", "termsAgreement": "Al continuar aceptas nuestros", "termsOfService": "términos de servicio", "privacyPolicy": "política de privacidad", "viewTransactionOn": "Ver Transacción en", "etherscan": "Etherscan"}, "claimStake": {"title": "Reclamar y apostar $THINK", "claimingDisabled": "<PERSON><PERSON><PERSON><PERSON>", "claimingDisabledDescription": "Los reclamos están actualmente deshabilitados.", "reachOutDiscord": "Contacta a discord si crees que esto es un error.", "transactionSuccessfullySent": "Transacción Enviada Exitosamente", "transactionBeingProcessed": "Tu transacción está siendo procesada. Por favor confirma {{amount}} $THINK en la transacción de abajo en Etherscan.", "transactionUnsuccessful": "Transacción No Exitosa", "errorOccurred": "Ha ocurrido un error durante la transacción. Por favor intenta de nuevo.", "youAreClaimingAll": "Estás reclamando todo $THINK", "preparingClaim": "Preparando <PERSON>lamo", "claimStakeInProgress": "Transacción de Reclamar y Apostar en Progreso", "claimInProgress": "Transacción de Reclamo en Progreso", "canTakeMinutes": "Esto puede tomar unos minutos. Por favor espera mientras se completa la transacción.", "youWillReceive": "Recibirás {{amount}} $THINK.", "cautionClaimed": "Precaución: El THINK reclamado no se transferirá con futuras ventas de NFT y NFI.", "inProgress": "en Progreso", "claimOnly": "<PERSON><PERSON><PERSON><PERSON> (solamente)", "claimAndStake": "Reclamar y Apostar", "tryAgain": "Intentar de Nuevo", "errorClickReload": "Error. <PERSON><PERSON> clic para Recargar", "termsAgreement": "Al continuar aceptas nuestros", "termsOfService": "términos de servicio", "privacyPolicy": "política de privacidad", "viewTransactionOn": "Ver Transacción en", "etherscan": "Etherscan"}}, "footer": {"navigation": {"about": "Acerca de", "investors": "Inversores", "partners": "<PERSON><PERSON><PERSON>", "docs": "Documentación", "contact": "Contacto"}, "legal": {"privacy": "Tu Privacidad", "terms": "Términos y Condiciones"}, "socialMedia": {"twitter": "X (Twitter)", "discord": "Únete a nuestro Discord", "youtube": "YouTube", "magicEden": "Colección Magic Eden"}}, "common": {"buttons": {"applyNow": "Aplica<PERSON>", "learnMore": "Aprender <PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON>", "submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "save": "Guardar", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "back": "Atrás", "next": "Siguient<PERSON>"}, "loading": "Cargando...", "error": "Error", "success": "Éxito", "warning": "Advertencia", "info": "Información", "aiYouOwn": "IA que Posees", "teamThink": "Equipo Think"}}
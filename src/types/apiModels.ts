import { Network } from "alchemy-sdk";

export interface TeamMember {
    name: string;
    title?: string;
    bio?: string;
    image?: string;
    url_linkedin?: string;
    url_x?: string;
    url_github?: string;
    url_ig?: string;
}

export interface InvestorsResponse {
    investors: LogoImage[];
}

export interface PartnersResponse {
    partners: LogoImage[];
}

export interface LogoImage {
    url?: string;
    name?: string;
    image?: string;
}

export interface FAQItem {
    id: number;
    question: string;
    answer?: string;
    is_open?: boolean;
    created_at: Date;
    updated_at: Date;
}

export interface WallerRetrieveData {
    wallets: {
       wallet: Wallet; 
       ensNfts: NFTData[];
       allNFTs: NFTData[];
    }[];
}

export interface GetWalletResponse {
    wallets: Wallet[];
}

export interface Wallet {
    address: string;
    authority: string;
    ens_domain: string;
    chain: string;
    frontend_user_id: string;
    created_at: Date;
    updated_at: Date;
    collections: string[];
    delegated_from_wallet_id: string;
    primary: boolean;
    avatar_url: string;
    use_ens_domain: boolean;
    use_avatar_url: boolean;
}

export interface NFTData { 
    contract: {
        address: string;
        name: string;
        symbol: string;
        totalSupply: string;
        tokenType: string;
        contractDeployer: string;
        deployedBlockNumber: number;
        openSeaMetadata: {
            floorPrice: number;
            collectionName: string;
            collectionSlug: string;
            safelistRequestStatus: string;
            imageUrl: string;
            description: string;
            lastIngestedAt: Date;
        };
        isSpam: boolean;
        spamClassifications: any[];
    };
    tokenId: string;
    tokenType: string;
    name: string;
    description: string;
    tokenUri: string;
    image: {
        cachedUrl: string;
        thumbnailUrl: string;
        pngUrl: string;
        contentType: string;
        size: number;
        originalUrl: string;
    };  
    animation: any;
    raw: {
        tokenUri: string;
        metadata: {
            name: string;
            description: string;
            image: string;
        };
    };
    collection: {
        name: string;
        slug: string;
    };
    mint: any;
    timeLastUpdated: Date;
    balance: string;
    acquiredAt: any;
}

export interface GetNFTResponse {
    allNfts: NetworkNfts[];
}

export interface NetworkNfts {
    network: string;
    nfts: OwnedNFTs;
    wallet: {
        address: string;
    };
}

export interface OwnedNFTs {
    ownedNfts: NFTData[];
    totalCount: number;
    validAt?: {
        blockHash: string;
        blockNumber: number;
        blockTimestamp: Date; 
    };
}

export interface GetTokenBalanceResponse {
    allTokens: NetworkTokenBalance[];
}

export interface NetworkTokenBalance {
    network: string;
    address: string;
    tokens: TokenBalance[];
}

export interface TokenBalance {
    contractAddress: string;
    tokenBalance: string;
    metadata: {
        decimals: number;
        logo?: string;
        name: string;
        symbol: string;
    };
}

export interface ClaimPayloadResponse {
    raw: any;
    payload: string;
    signature: string;
}

export interface ClaimsTransaction {
    status: string;
    claimPayload: ClaimPayloadResponse | null;
    details: {
        claimId: string;
        networkName?: Network;
        decimals: string;
        amount: string;
        claimAfter: number;
        claimBefore: number;
        claimableIds?: number[] | bigint[];
        pollTransactions: PollTransactionResponseItem[];
    } | null;
}

export interface CreateClaimsTransactionResponse extends ClaimsTransaction {
    eligibleWallet: string | null;
    message: string;
}

export interface EligibleSnapshotsResponse {
    eligibleWallet: string | null;
    message: string;
    claimables: EligibleSnapshot[];
}

export interface EligibleSnapshot {
    id: number | bigint;
    purpose: string | null;
    collectionName: string | null;
    networkName: string | null;
    tokenAmount: bigint;
    tokenDecimals: string;
    claimStatus?: string;
    claimableAfter: Date;
    claimableExpiry: Date | null;
    isGoldenTicket: boolean;
    lastClaim?: ClaimsTransaction;
}


export interface PollTransactionResponse {
    status: string;
    message: string;
    pollTransaction?: PollTransactionResponseItem;
}

export interface PollTransactionResponseItem {
    id: number;
    claimEntriesId: number;
    transactionHash: string;
    status: string;
    networkName: string;
    lastPolledAt: Date;
    nextPollAt: Date;
    pollFrequencyInSeconds: number;
    activePolling: boolean;
    createdAt: Date;
    updatedAt: Date;
}

export interface ClaimsTransaction {
    status: string;
    claimPayload: ClaimPayloadResponse | null;
    details: {
        claimId: string;
        networkName?: Network;
        decimals: string;
        amount: string;
        claimAfter: number;
        claimBefore: number;
        claimableIds?: number[] | bigint[];
        pollTransactions: PollTransactionResponseItem[];
    } | null;
}

export interface ActiveClaimEntriesResponse {
    eligibleWallet: string | null;
    message: string;
    activeClaims: ClaimsTransaction[];
}
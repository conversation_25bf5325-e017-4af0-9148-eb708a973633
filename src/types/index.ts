
export * from "./apiModels";

export interface IAttachment {
    url: string;
    contentType: string;
    title: string;
}

export interface User {
    address: `0x${string}`;
    userInitialSignedMessage?: string;
    avatarUrl?: string;
    ensDomain?: string;
}

export interface Post {
    title: string;
    body: string;
    created_at: Date;
    updated_at: Date;
    category: string;
    user_id: string;
    slug: string;
    image?: string;
    preview_image?: string;
}

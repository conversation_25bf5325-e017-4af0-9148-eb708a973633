import { sepolia } from "@account-kit/infra";

const ACCOUNT_KIT_API_KEY = '2O33Fxqgm5BQU8GDn0y7H37bEGFNo02P';
const WALLET_CONNECT_PROJECT_ID = '5abaec90f158fec2947edd5554b7a925';
const API_BASE_URL = 'https://staging.thinkagents.ai';
const WEB3_API_BASE_URL = 'https://staging.thinkagents.ai';
const WALLET_LOGIN = false; // Disabled to show home page
const PRIMARY_CHAIN = sepolia;
const ALCHEMY_KEY = `wss://eth-sepolia.g.alchemy.com/v2/${ACCOUNT_KIT_API_KEY}`;
const TOKEN_BURN_ENABLED = true;
const FV_AUTH_CLIENT_ID = "PklXCfN7vYY3lmMRbGprn";
const FV_CLIENT_NAME = "think-dev";
// const FV_AUTH_CLIENT_SECRET = "zulw2f1BURYLwCLsceZNpFAAWGShOY4rehS29q-6NBS";
const FV_ACCESS_TOKEN = "etCmyqPr-Oq--4p--tL7xvXLfE2biaaOhtXcYFZHJOy";
const FV_AUTH_REDIRECT_URI = "http://localhost:5173/fvlogin";
const FV_AUTH_POST_LOGOUT_REDIRECT_URI = "http://localhost:5173/fvlogout";
const FV_CLIENT_ID = "PklXCfN7vYY3lmMRbGprn";
const FV_WALLET_CONNECT_PROJECT_ID = "5abaec90f158fec2947edd5554b7a925";
const FV_XAMAN_API_KEY = "Ijp_rs2CSg8iZ8wQ4rDo9";
const FV_ENVIRONMENT = "development";
const THINKUBATOR_ENABLED = true;
export default {
    API_BASE_URL,
    WEB3_API_BASE_URL,
    WALLET_LOGIN,
    WALLET_CONNECT_PROJECT_ID,
    ACCOUNT_KIT_API_KEY,
    PRIMARY_CHAIN,
    ALCHEMY_KEY,
    TOKEN_BURN_ENABLED,
    FV_AUTH_CLIENT_ID,
    FV_ACCESS_TOKEN,
    FV_AUTH_REDIRECT_URI,
    FV_AUTH_POST_LOGOUT_REDIRECT_URI,
    FV_CLIENT_ID,
    FV_WALLET_CONNECT_PROJECT_ID,
    FV_XAMAN_API_KEY,
    FV_ENVIRONMENT,
    FV_CLIENT_NAME,
    THINKUBATOR_ENABLED
};

# Localization Guide

This project uses `react-i18next` for internationalization (i18n) support. **All text strings throughout the entire application have been localized** and the system is ready for additional languages.

## Setup

The localization system is automatically initialized when the app starts. The configuration is in `src/lib/i18n.ts`.

## Usage

### Using the Translation Hook

```tsx
import { useTranslation } from "@/hooks/useTranslation";

const MyComponent = () => {
  const { t } = useTranslation();
  
  return (
    <div>
      <h1>{t('header.navigation.about')}</h1>
      <p>{t('header.buttons.joinTheMovement')}</p>
    </div>
  );
};
```

### Available Translation Keys

All translation keys are defined in the `src/locales/en.json` file. Here are the main categories:

**Header Section:**
- `header.navigation.*` - Main navigation items
- `header.dropdown.*` - Dropdown menu items
- `header.buttons.*` - Button labels
- `header.countdown.*` - Countdown timer text
- `header.footer.*` - Footer links
- `header.accessibility.*` - Screen reader text
- `header.socialMedia.*` - Social media link titles

**Page Content:**
- `pages.home.*` - Home page content (hero, features, descriptions)
- `pages.about.*` - About page content (team, mission, descriptions)
- `pages.contact.*` - Contact page content (form labels, error messages)
- `pages.dashboard.*` - Dashboard content (greetings, rewards, staking)
- `pages.thinkubator.*` - Thinkubator page content (hero, descriptions)

**Common Elements:**
- `common.buttons.*` - Reusable button labels
- `common.loading` - Loading states
- `common.error` - Error messages
- `common.success` - Success messages

### Adding New Languages

1. Create a new translation file in `src/locales/` (e.g., `fr.json`)
2. Copy the structure from `en.json` and translate all values
3. Import the new translations in `src/lib/i18n.ts`
4. Add the language to the resources object

Example:
```typescript
// In src/lib/i18n.ts
import frTranslations from '../locales/fr.json';

const resources = {
  en: { translation: enTranslations },
  es: { translation: esTranslations },
  fr: { translation: frTranslations }, // Add new language
};
```

### Language Switching

Use the `LanguageSwitcher` component or the `changeLanguage` function:

```tsx
import { LanguageSwitcher } from "@/components/ui/LanguageSwitcher";
import { useTranslation } from "@/hooks/useTranslation";

// Using the component
<LanguageSwitcher />

// Using the hook directly
const { changeLanguage } = useTranslation();
changeLanguage('es'); // Switch to Spanish
```

### Type Safety

The `useTranslation` hook includes TypeScript types for all available translation keys, providing IDE autocompletion and compile-time checking.

## Current Languages

- **English (en)** - Default language with complete translations
- **Spanish (es)** - Complete translations for all pages and components

## File Structure

```
src/
├── lib/
│   └── i18n.ts              # i18n configuration
├── hooks/
│   └── useTranslation.ts    # Custom translation hook
├── locales/
│   ├── en.json             # English translations
│   └── es.json             # Spanish translations
└── components/
    └── ui/
        └── LanguageSwitcher.tsx  # Language switcher component
```

## Best Practices

1. Always use the translation function `t()` for user-facing text
2. Use descriptive, hierarchical keys (e.g., `header.navigation.about`)
3. Keep translation files organized by feature/component
4. Test all languages before deploying
5. Consider pluralization rules for different languages
6. Use interpolation for dynamic content: `t('welcome', { name: 'John' })`

## Localized Components

The following components and pages have been fully localized:

### Pages
- **Home** (`src/routes/home.tsx`) - Hero section, features, descriptions
- **About** (`src/routes/about.tsx`) - Team information, mission, descriptions
- **Contact** (`src/routes/contact.tsx`) - Form labels, error messages, placeholders
- **Dashboard** (`src/routes/dashboard/dashboard.tsx`) - User greetings, rewards, staking interface
- **Thinkubator** (`src/routes/thinkubator.tsx`) - Hero content, descriptions, navigation

### Components
- **Header** (`src/components/layouts/header.tsx`) - Navigation, dropdowns, buttons, accessibility text

## Adding Translations to New Components

When creating new components with text content:

1. Add translation keys to the appropriate locale files following the established hierarchy
2. Use the `useTranslation` hook in your component: `const { t } = useTranslation();`
3. Replace hardcoded strings with `t('your.translation.key')`
4. Update the TypeScript types in `useTranslation.ts` to include new keys
5. Add corresponding translations to all language files (en.json, es.json, etc.)

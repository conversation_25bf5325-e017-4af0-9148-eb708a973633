#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

/**
 * Simple script to find hardcoded text strings in React components
 * Usage: node scripts/extract-i18n.js [directory]
 */

const srcDir = process.argv[2] || 'src';

// Common patterns for hardcoded text in JSX
const patterns = [
  // JSX text content: <div>text</div>
  /<[^>]*>([^<>{]*\w[^<>{}]*)</g,
  // String literals in JSX: "text", 'text'
  /["']([^"']*\w[^"']*?)["']/g,
  // JSX attributes: placeholder="text"
  /\w+\s*=\s*["']([^"']*\w[^"']*?)["']/g,
];

function extractStringsFromFile(filePath) {
  if (!filePath.endsWith('.tsx') && !filePath.endsWith('.jsx')) {
    return [];
  }

  const content = fs.readFileSync(filePath, 'utf-8');
  const strings = new Set();

  patterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const text = match[1];
      if (text && text.trim() && !text.includes('{') && !text.includes('$')) {
        strings.add(text.trim());
      }
    }
  });

  return Array.from(strings);
}

function scanDirectory(dir) {
  const results = {};

  function walk(currentPath) {
    const files = fs.readdirSync(currentPath);

    files.forEach(file => {
      const filePath = path.join(currentPath, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        walk(filePath);
      } else if (file.endsWith('.tsx') || file.endsWith('.jsx')) {
        const strings = extractStringsFromFile(filePath);
        if (strings.length > 0) {
          results[filePath] = strings;
        }
      }
    });
  }

  walk(dir);
  return results;
}

// Main execution
console.log('🔍 Scanning for hardcoded text strings...\n');
const results = scanDirectory(srcDir);

Object.entries(results).forEach(([file, strings]) => {
  console.log(`📄 ${file}:`);
  strings.forEach(str => {
    console.log(`  - "${str}"`);
  });
  console.log();
});

console.log('✅ Scan complete!');
console.log('💡 Consider moving these strings to src/locales/en/translation.json');
console.log('💡 Use t("key") in your components with useTranslation hook');
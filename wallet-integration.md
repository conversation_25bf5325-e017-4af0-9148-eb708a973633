# Wallet Integration Documentation

This document explains how user wallet addresses are stored and passed with messages in the application.

## Overview

When a user connects their wallet to the application, their wallet address is stored in localStorage and automatically included with all messages sent to the backend. This allows the backend to associate messages with specific users.

## Implementation Details

### Storage

- The wallet address is stored in localStorage under the key `user_wallet_address`
- This happens in multiple places to ensure the address is always available:
  - In the `WalletButton` component's useEffect hook when a user connects
  - In the `WalletInfoModal` component when a user logs out (to remove the address)
  - In the `AuthWrapper` component during the authentication flow

### Passing with Messages

The wallet address is passed with messages in two ways:

1. **API Client Integration**: The `apiClient.sendMessage` method automatically includes the user's wallet address with every message sent to the backend as a `userWalletAddress` field in the FormData.

2. **Local Message Store**: The wallet address is also included in the local message store maintained by React Query, allowing for potential client-side filtering or display of user-specific information.

## Utilities

Several utilities have been created to make working with wallet addresses easier:

### `useWalletUser` Hook

A custom React hook that provides:
- The current wallet address
- The user object from @account-kit/react
- A boolean `isConnected` flag indicating if a wallet is connected

```typescript
const { user, walletAddress, isConnected } = useWalletUser();
```

### `getCurrentWalletAddress` Function

A synchronous function to get the current user's wallet address:

```typescript
import { getCurrentWalletAddress } from '@/hooks/use-wallet-user';

// Can be called outside of React components
const address = getCurrentWalletAddress();
```

### `attachWalletAddress` Function

A utility function that attaches the user's wallet address to any object:

```typescript
import { attachWalletAddress } from '@/hooks/use-wallet-user';

const data = { message: 'Hello world' };
const dataWithUser = attachWalletAddress(data);
// Result: { message: 'Hello world', userWalletAddress: '0x123...' }
```

## Backend Considerations

The backend API should expect messages to contain a `userWalletAddress` field and use this to:

1. Associate messages with specific users
2. Store message history per user
3. Implement user-specific features or permissions
4. Track user engagement and activity

## Security Considerations

- The wallet address is stored in localStorage, which means it's accessible to any JavaScript running on the page.
- For increased security in production, consider using more secure storage options like cookies with HttpOnly flag.
- Always validate wallet ownership on the server side for important operations.
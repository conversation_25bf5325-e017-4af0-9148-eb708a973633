#!/bin/bash

registry=plenipotentss/thinkagents-ui

usage() {
  echo ""
  echo "Usage: $0 [OPTIONS]"
  echo ""
  echo "Builds current app through docker"
  echo ""
  echo "  --build       builds the docker image"
  echo "  --push        pushes the docker image to registry"
  echo "  --run         runs docker image locally"
  echo "  --help        usage information"
  echo ""
  exit 1
}
tag="$(git rev-parse --abbrev-ref HEAD | sed 's/[^a-zA-Z0-9._-]/-/g')-$(git rev-parse --short HEAD)"

while [ $# -gt 0 ]; do
  case "$1" in
    --build)
      build=true
      shift
      ;;
    --push)
      push=true
      shift
      ;;
    --run)
      run=true
      shift
      ;;
    --help)
      usage
      ;;
    *)
      echo "Invalid option: $1"
      usage
      exit 1
      ;;
  esac
done

if [ -z "$build" ] && [ -z "$push" ] && [ -z "$run" ]; then
  usage
  exit 1
fi


if [ -n "$build" ]; then
  docker build -f ./Dockerfile . -t "$registry:$tag"
fi

if [ -n "$push" ]; then
  docker push "$registry:$tag"
fi

if [ -n "$run" ]; then
  docker run -p 8081:80 "$registry:$tag"
fi

exit 1